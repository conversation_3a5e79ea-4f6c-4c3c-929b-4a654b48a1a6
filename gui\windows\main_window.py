"""
النافذة الرئيسية للتطبيق
"""
import sys
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
    QPushButton, QFrame, QStackedWidget, QMenuBar, QMenu,
    QStatusBar, QMessageBox, QApplication, QGridLayout
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QIcon, QAction

from gui.windows.login_window import LoginWindow
from gui.widgets.dashboard_widget import DashboardWidget
from gui.widgets.subscribers_widget import SubscribersWidget
from gui.widgets.routers_widget import RoutersWidget
from gui.widgets.transactions_widget import TransactionsWidget
from app.core.config import settings


class MainWindow(QMainWindow):
    """النافذة الرئيسية"""
    
    def __init__(self):
        super().__init__()
        self.current_user = None
        self.access_token = None
        self.widgets = {}
        
        self.init_ui()
        self.show_login()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle(settings.app_name)
        self.setMinimumSize(1200, 800)
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        
        # إنشاء القائمة الرئيسية
        self.create_menu_bar()
        
        # إنشاء شريط الحالة
        self.create_status_bar()
        
        # إنشاء الواجهة الرئيسية
        self.create_main_interface()
        
        # تطبيق الأنماط
        self.apply_styles()
    
    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة الملف
        file_menu = menubar.addMenu("ملف")
        
        logout_action = QAction("تسجيل الخروج", self)
        logout_action.triggered.connect(self.logout)
        file_menu.addAction(logout_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("خروج", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة العرض
        view_menu = menubar.addMenu("عرض")
        
        dashboard_action = QAction("لوحة التحكم", self)
        dashboard_action.triggered.connect(lambda: self.show_widget("dashboard"))
        view_menu.addAction(dashboard_action)
        
        # قائمة المساعدة
        help_menu = menubar.addMenu("مساعدة")
        
        about_action = QAction("حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        self.status_bar = self.statusBar()
        
        # معلومات المستخدم
        self.user_label = QLabel("غير متصل")
        self.status_bar.addPermanentWidget(self.user_label)
        
        # الوقت
        self.time_label = QLabel()
        self.status_bar.addPermanentWidget(self.time_label)
        
        # تحديث الوقت كل ثانية
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)
        self.update_time()
    
    def create_main_interface(self):
        """إنشاء الواجهة الرئيسية"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # الشريط الجانبي
        self.create_sidebar(main_layout)
        
        # المحتوى الرئيسي
        self.create_content_area(main_layout)
    
    def create_sidebar(self, parent_layout):
        """إنشاء الشريط الجانبي"""
        sidebar = QFrame()
        sidebar.setObjectName("sidebar")
        sidebar.setFixedWidth(250)
        
        sidebar_layout = QVBoxLayout(sidebar)
        sidebar_layout.setContentsMargins(10, 20, 10, 20)
        sidebar_layout.setSpacing(10)
        
        # شعار التطبيق
        logo_label = QLabel("💼")
        logo_label.setObjectName("logoLabel")
        logo_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        sidebar_layout.addWidget(logo_label)
        
        app_name_label = QLabel("تطبيق المحاسبة")
        app_name_label.setObjectName("appNameLabel")
        app_name_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        sidebar_layout.addWidget(app_name_label)
        
        # خط فاصل
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setFrameShadow(QFrame.Shadow.Sunken)
        sidebar_layout.addWidget(line)
        
        # أزرار التنقل
        self.nav_buttons = {}
        
        nav_items = [
            ("dashboard", "🏠", "لوحة التحكم"),
            ("subscribers", "👥", "المشتركين"),
            ("routers", "📡", "الراوترات"),
            ("packages", "📦", "الباقات"),
            ("transactions", "💰", "المعاملات"),
            ("inventory", "📋", "المخزون"),
            ("cables", "🔌", "الكبلات"),
            ("reports", "📊", "التقارير"),
        ]
        
        for key, icon, text in nav_items:
            button = QPushButton(f"{icon} {text}")
            button.setObjectName("navButton")
            button.clicked.connect(lambda checked, k=key: self.show_widget(k))
            self.nav_buttons[key] = button
            sidebar_layout.addWidget(button)
        
        # مساحة فارغة
        sidebar_layout.addStretch()
        
        # زر تسجيل الخروج
        logout_button = QPushButton("🚪 تسجيل الخروج")
        logout_button.setObjectName("logoutButton")
        logout_button.clicked.connect(self.logout)
        sidebar_layout.addWidget(logout_button)
        
        parent_layout.addWidget(sidebar)
    
    def create_content_area(self, parent_layout):
        """إنشاء منطقة المحتوى"""
        content_frame = QFrame()
        content_frame.setObjectName("contentFrame")
        
        content_layout = QVBoxLayout(content_frame)
        content_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الصفحة
        self.page_title = QLabel("لوحة التحكم")
        self.page_title.setObjectName("pageTitle")
        content_layout.addWidget(self.page_title)
        
        # المحتوى المتغير
        self.stacked_widget = QStackedWidget()
        content_layout.addWidget(self.stacked_widget)
        
        parent_layout.addWidget(content_frame)
    
    def apply_styles(self):
        """تطبيق الأنماط"""
        style = """
        QMainWindow {
            background-color: #f5f5f5;
        }
        
        QFrame#sidebar {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #667eea, stop:1 #764ba2);
            border-right: 1px solid #ddd;
        }
        
        QLabel#logoLabel {
            font-size: 48px;
            margin: 20px 0;
        }
        
        QLabel#appNameLabel {
            font-size: 16px;
            font-weight: bold;
            color: white;
            margin-bottom: 20px;
        }
        
        QPushButton#navButton {
            text-align: right;
            padding: 15px 20px;
            font-size: 14px;
            color: white;
            background: transparent;
            border: none;
            border-radius: 8px;
            margin: 2px 0;
        }
        
        QPushButton#navButton:hover {
            background: rgba(255, 255, 255, 0.1);
        }
        
        QPushButton#navButton:pressed {
            background: rgba(255, 255, 255, 0.2);
        }
        
        QPushButton#logoutButton {
            padding: 12px 20px;
            font-size: 14px;
            color: white;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
        }
        
        QPushButton#logoutButton:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        QFrame#contentFrame {
            background: white;
            border-radius: 10px;
            margin: 10px;
        }
        
        QLabel#pageTitle {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
        }
        
        QStatusBar {
            background: #f8f9fa;
            border-top: 1px solid #dee2e6;
        }
        """
        self.setStyleSheet(style)
    
    def show_login(self):
        """عرض نافذة تسجيل الدخول"""
        login_window = LoginWindow()
        login_window.login_successful.connect(self.on_login_success)
        
        # إخفاء النافذة الرئيسية
        self.hide()

        # عرض نافذة تسجيل الدخول
        login_window.show()

        # انتظار نتيجة تسجيل الدخول
        login_window.login_successful.connect(self.on_login_success)
        login_window.login_cancelled.connect(self.on_login_failed)
    
    def on_login_success(self, result):
        """نجح تسجيل الدخول"""
        self.current_user = result["user"]
        self.access_token = result["access_token"]
        
        # تحديث شريط الحالة
        self.user_label.setText(f"مرحباً، {self.current_user['full_name']} ({self.current_user['role']})")
        
        # إنشاء الواجهات
        self.create_widgets()
        
        # عرض لوحة التحكم
        self.show_widget("dashboard")
        
        # إظهار النافذة الرئيسية
        self.show()
        self.showMaximized()

    def on_login_failed(self):
        """فشل تسجيل الدخول"""
        self.close()

    def create_widgets(self):
        """إنشاء واجهات التطبيق"""
        # لوحة التحكم
        self.widgets["dashboard"] = DashboardWidget(self.current_user, self.access_token)
        self.stacked_widget.addWidget(self.widgets["dashboard"])
        
        # المشتركين
        self.widgets["subscribers"] = SubscribersWidget(self.current_user, self.access_token)
        self.stacked_widget.addWidget(self.widgets["subscribers"])
        
        # الراوترات
        self.widgets["routers"] = RoutersWidget(self.current_user, self.access_token)
        self.stacked_widget.addWidget(self.widgets["routers"])
        
        # المعاملات
        self.widgets["transactions"] = TransactionsWidget(self.current_user, self.access_token)
        self.stacked_widget.addWidget(self.widgets["transactions"])
        
        # يمكن إضافة المزيد من الواجهات هنا
    
    def show_widget(self, widget_name):
        """عرض واجهة محددة"""
        if widget_name in self.widgets:
            self.stacked_widget.setCurrentWidget(self.widgets[widget_name])
            
            # تحديث عنوان الصفحة
            titles = {
                "dashboard": "لوحة التحكم",
                "subscribers": "إدارة المشتركين",
                "routers": "إدارة الراوترات",
                "packages": "إدارة الباقات",
                "transactions": "المعاملات المالية",
                "inventory": "إدارة المخزون",
                "cables": "إدارة الكبلات",
                "reports": "التقارير والإحصائيات"
            }
            
            self.page_title.setText(titles.get(widget_name, ""))
            
            # تحديث أزرار التنقل
            for key, button in self.nav_buttons.items():
                if key == widget_name:
                    button.setStyleSheet("background: rgba(255, 255, 255, 0.2);")
                else:
                    button.setStyleSheet("")
    
    def update_time(self):
        """تحديث الوقت"""
        from datetime import datetime
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.setText(current_time)
    
    def logout(self):
        """تسجيل الخروج"""
        reply = QMessageBox.question(
            self, "تسجيل الخروج", 
            "هل أنت متأكد من تسجيل الخروج؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.current_user = None
            self.access_token = None
            self.hide()
            self.show_login()
    
    def show_about(self):
        """عرض معلومات البرنامج"""
        QMessageBox.about(
            self, "حول البرنامج",
            f"""
            <h3>{settings.app_name}</h3>
            <p><b>الإصدار:</b> {settings.app_version}</p>
            <p><b>الوصف:</b> تطبيق محاسبة متكامل لإدارة المشتركين والعمليات المحاسبية</p>
            <p><b>المطور:</b> فريق التطوير</p>
            <p><b>حقوق النشر:</b> © 2025 جميع الحقوق محفوظة</p>
            """
        )
    
    def closeEvent(self, event):
        """معالجة إغلاق النافذة"""
        reply = QMessageBox.question(
            self, "إغلاق التطبيق",
            "هل أنت متأكد من إغلاق التطبيق؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            event.accept()
        else:
            event.ignore()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
    
    window = MainWindow()
    window.show()
    
    sys.exit(app.exec())
