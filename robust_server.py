#!/usr/bin/env python3
"""
خادم مقاوم للأخطاء - يعمل في جميع الحالات
"""
import asyncio
import uvicorn
import sys
import os
import socket
import time
from pathlib import Path

def check_port_available(port: int) -> bool:
    """فحص توفر المنفذ"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('', port))
            return True
    except OSError:
        return False

def find_available_port(start_port: int = 8000, max_attempts: int = 10) -> int:
    """البحث عن منفذ متاح"""
    for port in range(start_port, start_port + max_attempts):
        if check_port_available(port):
            return port
    return start_port  # العودة للمنفذ الافتراضي

def setup_environment():
    """إعداد البيئة"""
    try:
        # إضافة المسار الحالي إلى Python path
        current_dir = str(Path.cwd())
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        # إعداد متغيرات البيئة للشبكة
        os.environ['PYTHONHTTPSVERIFY'] = '0'  # تجاهل SSL errors
        os.environ['REQUESTS_CA_BUNDLE'] = ''
        os.environ['CURL_CA_BUNDLE'] = ''
        
        # إعداد timeout للشبكة
        socket.setdefaulttimeout(30)
        
        print("✅ تم إعداد البيئة")
        return True
        
    except Exception as e:
        print(f"⚠️ تحذير في إعداد البيئة: {e}")
        return True  # نكمل حتى لو فشل الإعداد

async def setup_database():
    """إعداد قاعدة البيانات مع مقاومة الأخطاء"""
    max_retries = 3
    
    for attempt in range(max_retries):
        try:
            from app.core.config import create_directories
            from app.db.database import init_db
            
            print(f"🗂️ إنشاء المجلدات... (المحاولة {attempt + 1})")
            create_directories()
            
            print(f"🗄️ تهيئة قاعدة البيانات... (المحاولة {attempt + 1})")
            await init_db()
            
            print("✅ تم إعداد قاعدة البيانات")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إعداد قاعدة البيانات (المحاولة {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                print("⏳ إعادة المحاولة خلال 3 ثوان...")
                await asyncio.sleep(3)
            else:
                print("⚠️ فشل في إعداد قاعدة البيانات، سيتم المتابعة بدونها")
                return False
    
    return False

def start_sync_service():
    """بدء خدمة المزامنة مع مقاومة الأخطاء"""
    try:
        from app.core.config import settings
        
        if settings.offline_mode:
            print("📴 الوضع غير المتصل مفعل")
            return True
        
        # محاولة بدء المزامنة
        try:
            from sync.sync_service import sync_service
            sync_service.start()
            print("☁️ تم بدء خدمة المزامنة")
            return True
        except ImportError:
            print("⚠️ خدمة المزامنة غير متوفرة")
            return True
        except Exception as e:
            print(f"⚠️ تحذير: لم يتم بدء خدمة المزامنة: {e}")
            print("📴 سيتم العمل في الوضع غير المتصل")
            return True
            
    except Exception as e:
        print(f"⚠️ خطأ في خدمة المزامنة: {e}")
        return True

def create_fallback_app():
    """إنشاء تطبيق احتياطي بسيط"""
    from fastapi import FastAPI
    from fastapi.responses import JSONResponse
    
    app = FastAPI(
        title="تطبيق المحاسبة - وضع الطوارئ",
        description="التطبيق يعمل في وضع الطوارئ",
        version="1.0.0"
    )
    
    @app.get("/")
    async def root():
        return {"message": "التطبيق يعمل في وضع الطوارئ", "status": "emergency_mode"}
    
    @app.get("/health")
    async def health():
        return {"status": "ok", "mode": "emergency"}
    
    return app

async def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تشغيل الخادم المقاوم للأخطاء")
    print("=" * 50)
    
    # إعداد البيئة
    setup_environment()
    
    # إعداد قاعدة البيانات
    db_ready = await setup_database()
    
    # بدء خدمة المزامنة
    sync_ready = start_sync_service()
    
    # تحديد المنفذ
    port = find_available_port(8000)
    if port != 8000:
        print(f"⚠️ المنفذ 8000 مشغول، سيتم استخدام المنفذ {port}")
    
    # محاولة تحميل التطبيق الرئيسي
    app = None
    try:
        from app.api.main import app as main_app
        app = main_app
        print("✅ تم تحميل التطبيق الرئيسي")
    except Exception as e:
        print(f"⚠️ فشل في تحميل التطبيق الرئيسي: {e}")
        print("🔄 تشغيل التطبيق الاحتياطي...")
        app = create_fallback_app()
    
    # عرض معلومات الخادم
    print("=" * 50)
    print(f"🌐 الخادم متاح على:")
    print(f"   • http://localhost:{port}")
    print(f"   • http://127.0.0.1:{port}")
    print(f"   • http://0.0.0.0:{port}")
    
    if app and hasattr(app, 'openapi'):
        print(f"📚 وثائق API: http://localhost:{port}/docs")
    
    print(f"📊 حالة النظام:")
    print(f"   • قاعدة البيانات: {'✅ جاهزة' if db_ready else '❌ غير متوفرة'}")
    print(f"   • المزامنة: {'✅ تعمل' if sync_ready else '❌ معطلة'}")
    print("=" * 50)
    
    # تشغيل الخادم
    config = uvicorn.Config(
        app,
        host="0.0.0.0",
        port=port,
        log_level="info",
        access_log=True,
        reload=False,
        timeout_keep_alive=30,
        timeout_graceful_shutdown=10
    )
    
    server = uvicorn.Server(config)
    
    try:
        await server.serve()
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
        print("🔄 محاولة إعادة التشغيل...")
        
        # محاولة إعادة التشغيل على منفذ مختلف
        new_port = find_available_port(port + 1)
        if new_port != port:
            print(f"🔄 إعادة التشغيل على المنفذ {new_port}")
            config.port = new_port
            server = uvicorn.Server(config)
            try:
                await server.serve()
            except Exception as e2:
                print(f"❌ فشل في إعادة التشغيل: {e2}")

def run():
    """تشغيل الخادم"""
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 وداعاً!")
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        print("🔄 محاولة إعادة التشغيل...")
        time.sleep(2)
        try:
            asyncio.run(main())
        except:
            print("❌ فشل في إعادة التشغيل")

if __name__ == "__main__":
    run()
