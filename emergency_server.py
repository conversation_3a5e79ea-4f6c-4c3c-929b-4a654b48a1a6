#!/usr/bin/env python3
"""
خادم الطوارئ - يعمل في جميع الحالات
"""
print("🚀 بدء خادم الطوارئ...")

try:
    import uvicorn
    from fastapi import FastAPI, HTTPException
    from fastapi.responses import JSONResponse
    from fastapi.middleware.cors import CORSMiddleware
    
    # إنشاء التطبيق
    app = FastAPI(
        title="تطبيق المحاسبة - وضع الطوارئ",
        description="التطبيق يعمل في وضع الطوارئ مع الوظائف الأساسية",
        version="1.0.0-emergency"
    )
    
    # إعداد CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    @app.get("/")
    async def root():
        return {
            "message": "🏢 تطبيق المحاسبة يعمل!",
            "status": "emergency_mode",
            "version": "1.0.0",
            "docs": "/docs"
        }
    
    @app.get("/health")
    async def health():
        return {"status": "healthy", "mode": "emergency"}
    
    @app.get("/status")
    async def status():
        import sys
        import os
        return {
            "python_version": sys.version,
            "working_directory": os.getcwd(),
            "mode": "emergency",
            "features": {
                "database": "❌ غير متوفرة في وضع الطوارئ",
                "sync": "❌ غير متوفرة في وضع الطوارئ",
                "gui": "❌ غير متوفرة في وضع الطوارئ",
                "api": "✅ متوفرة"
            }
        }
    
    # محاولة تحميل قاعدة البيانات
    try:
        import asyncio
        import sys
        import os
        
        # إضافة المسار الحالي
        sys.path.insert(0, os.getcwd())
        
        from app.core.config import create_directories
        from app.db.database import init_db
        
        async def setup_db():
            try:
                create_directories()
                await init_db()
                return True
            except Exception as e:
                print(f"⚠️ فشل في إعداد قاعدة البيانات: {e}")
                return False
        
        # تشغيل إعداد قاعدة البيانات
        db_ready = asyncio.run(setup_db())
        
        if db_ready:
            print("✅ قاعدة البيانات جاهزة")
            
            # إضافة endpoints قاعدة البيانات
            @app.get("/api/test-db")
            async def test_db():
                try:
                    from app.db.database import get_db
                    from app.models.user import User
                    
                    db = next(get_db())
                    users = db.query(User).limit(5).all()
                    
                    return {
                        "status": "success",
                        "users_count": len(users),
                        "message": "قاعدة البيانات تعمل بشكل صحيح"
                    }
                except Exception as e:
                    raise HTTPException(status_code=500, detail=f"خطأ في قاعدة البيانات: {e}")
        else:
            print("❌ قاعدة البيانات غير متوفرة")
    
    except Exception as e:
        print(f"⚠️ لا يمكن تحميل قاعدة البيانات: {e}")
        print("📴 سيتم العمل في وضع API فقط")
    
    # معلومات التشغيل
    print("=" * 50)
    print("✅ خادم الطوارئ جاهز!")
    print("🌐 الخادم متاح على:")
    print("   • http://localhost:8000")
    print("   • http://127.0.0.1:8000")
    print("📚 وثائق API: http://localhost:8000/docs")
    print("🔍 حالة النظام: http://localhost:8000/status")
    print("=" * 50)
    
    # تشغيل الخادم
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info"
    )

except ImportError as e:
    print(f"❌ مكتبة مفقودة: {e}")
    print("📦 يرجى تثبيت المتطلبات:")
    print("   pip install fastapi uvicorn")
    input("اضغط Enter للخروج...")

except Exception as e:
    print(f"❌ خطأ في تشغيل الخادم: {e}")
    input("اضغط Enter للخروج...")
