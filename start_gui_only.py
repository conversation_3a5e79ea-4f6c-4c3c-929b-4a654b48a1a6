#!/usr/bin/env python3
"""
تشغيل واجهة سطح المكتب فقط
"""
import sys
import os
import asyncio
from pathlib import Path

def setup_environment():
    """إعداد البيئة"""
    try:
        # إضافة المسار الحالي
        current_dir = str(Path.cwd())
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        print("✅ تم إعداد البيئة")
        return True
        
    except Exception as e:
        print(f"⚠️ تحذير في إعداد البيئة: {e}")
        return True

async def setup_database():
    """إعداد قاعدة البيانات"""
    try:
        from app.core.config import create_directories, settings
        from app.db.database import init_db
        
        print("🗂️ إنشاء المجلدات...")
        create_directories()
        
        print("🗄️ تهيئة قاعدة البيانات...")
        await init_db()
        
        print("✅ تم إعداد قاعدة البيانات")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
        return False

def run_gui():
    """تشغيل واجهة المستخدم"""
    try:
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt
        from gui.windows.login_window import LoginWindow
        
        print("🖥️ بدء تشغيل واجهة المستخدم...")
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setApplicationName("تطبيق المحاسبة المتكامل")
        app.setApplicationVersion("1.0.0")
        
        # تطبيق الستايل العربي
        app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        
        # تطبيق الأنماط
        app.setStyleSheet("""
        QApplication {
            font-family: 'Segoe UI', 'Tahoma', 'Arial';
            font-size: 11pt;
        }
        
        QMainWindow, QWidget {
            background-color: #f5f5f5;
        }
        
        QLabel {
            color: #2c3e50;
            margin: 5px;
        }
        
        QPushButton {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-size: 12pt;
            margin: 5px;
        }
        
        QPushButton:hover {
            background-color: #2980b9;
        }
        
        QPushButton:pressed {
            background-color: #21618c;
        }
        
        QPushButton:disabled {
            background-color: #bdc3c7;
        }
        
        QLineEdit {
            padding: 8px;
            border: 2px solid #bdc3c7;
            border-radius: 5px;
            font-size: 12pt;
            margin: 5px;
        }
        
        QLineEdit:focus {
            border-color: #3498db;
        }
        """)
        
        # إنشاء نافذة تسجيل الدخول
        login_window = LoginWindow()
        
        # ربط الإشارات
        def on_login_success(result):
            print(f"✅ تم تسجيل الدخول بنجاح: {result['user']['username']}")
            login_window.hide()
            
            # إنشاء النافذة الرئيسية
            try:
                from gui.windows.main_window import MainWindow
                main_window = MainWindow()
                main_window.current_user = result["user"]
                main_window.access_token = result["access_token"]
                
                # تحديث واجهة المستخدم
                main_window.user_label.setText(f"مرحباً، {result['user']['full_name']} ({result['user']['role']})")
                
                # إنشاء الواجهات
                main_window.create_widgets()
                
                # عرض لوحة التحكم
                main_window.show_widget("dashboard")
                
                # إظهار النافذة الرئيسية
                main_window.show()
                main_window.showMaximized()
                
                print("✅ تم فتح النافذة الرئيسية")
                
            except Exception as e:
                print(f"❌ خطأ في فتح النافذة الرئيسية: {e}")
                from PyQt6.QtWidgets import QMessageBox
                QMessageBox.critical(None, "خطأ", f"لا يمكن فتح النافذة الرئيسية:\n{e}")
        
        def on_login_cancelled():
            print("❌ تم إلغاء تسجيل الدخول")
            app.quit()
        
        # ربط الإشارات
        login_window.login_successful.connect(on_login_success)
        login_window.login_cancelled.connect(on_login_cancelled)
        
        # عرض نافذة تسجيل الدخول
        login_window.show()
        login_window.raise_()
        login_window.activateWindow()
        
        print("✅ تم عرض نافذة تسجيل الدخول")
        print("🔑 بيانات تسجيل الدخول الافتراضية:")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")
        
        # تشغيل التطبيق
        return app.exec()
        
    except ImportError as e:
        print(f"❌ مكتبة مفقودة: {e}")
        print("📦 يرجى تثبيت PyQt6:")
        print("   pip install PyQt6")
        return 1
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل الواجهة: {e}")
        return 1

async def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تشغيل واجهة المستخدم")
    print("=" * 50)
    
    # إعداد البيئة
    setup_environment()
    
    # إعداد قاعدة البيانات
    db_ready = await setup_database()
    
    if not db_ready:
        print("⚠️ تحذير: قاعدة البيانات غير متوفرة")
        print("💡 بعض الوظائف قد لا تعمل بشكل صحيح")
    
    print("=" * 50)
    
    # تشغيل الواجهة
    return run_gui()

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
        input("اضغط Enter للخروج...")
        sys.exit(1)
