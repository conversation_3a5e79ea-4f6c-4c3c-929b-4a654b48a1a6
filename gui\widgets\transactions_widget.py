"""
واجهة إدارة المعاملات المالية
"""
import requests
from datetime import datetime, date
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QLineEdit, QLabel, QMessageBox, QHeaderView, QFrame,
    QComboBox, QDateEdit, QDialog, QFormLayout, QDialogButtonBox,
    QDoubleSpinBox, QTextEdit, QTabWidget
)
from PyQt6.QtCore import Qt, QDate
from PyQt6.QtGui import QFont

from app.core.config import settings


class AddTransactionDialog(QDialog):
    """نافذة إضافة معاملة جديدة"""
    
    def __init__(self, access_token, parent=None):
        super().__init__(parent)
        self.access_token = access_token
        self.init_ui()
    
    def init_ui(self):
        """تهيئة واجهة النافذة"""
        self.setWindowTitle("إضافة معاملة جديدة")
        self.setModal(True)
        self.resize(400, 500)
        
        layout = QVBoxLayout(self)
        
        # نموذج البيانات
        form_layout = QFormLayout()
        
        # نوع المعاملة
        self.type_combo = QComboBox()
        self.type_combo.addItems([
            "subscription", "renewal", "router_sale", "service_fee",
            "expense", "salary", "purchase", "refund"
        ])
        form_layout.addRow("نوع المعاملة:", self.type_combo)
        
        # التصنيف
        self.category_input = QLineEdit()
        self.category_input.setPlaceholderText("تصنيف فرعي (اختياري)")
        form_layout.addRow("التصنيف:", self.category_input)
        
        # المبلغ
        self.amount_input = QDoubleSpinBox()
        self.amount_input.setRange(0, 999999)
        self.amount_input.setDecimals(2)
        self.amount_input.setSuffix(" USD")
        form_layout.addRow("المبلغ:", self.amount_input)
        
        # طريقة الدفع
        self.payment_method_combo = QComboBox()
        self.payment_method_combo.addItems(["cash", "card", "bank_transfer", "check"])
        form_layout.addRow("طريقة الدفع:", self.payment_method_combo)
        
        # المرجع
        self.reference_input = QLineEdit()
        self.reference_input.setPlaceholderText("رقم مرجعي (اختياري)")
        form_layout.addRow("المرجع:", self.reference_input)
        
        # الوصف
        self.description_input = QLineEdit()
        self.description_input.setPlaceholderText("وصف المعاملة")
        form_layout.addRow("الوصف:", self.description_input)
        
        # ملاحظات
        self.notes_input = QTextEdit()
        self.notes_input.setPlaceholderText("ملاحظات إضافية (اختياري)")
        self.notes_input.setMaximumHeight(60)
        form_layout.addRow("ملاحظات:", self.notes_input)
        
        layout.addLayout(form_layout)
        
        # أزرار التحكم
        buttons = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
    
    def get_data(self):
        """الحصول على بيانات النموذج"""
        return {
            "type": self.type_combo.currentText(),
            "category": self.category_input.text().strip() or None,
            "amount": self.amount_input.value(),
            "payment_method": self.payment_method_combo.currentText(),
            "payment_reference": self.reference_input.text().strip() or None,
            "description": self.description_input.text().strip(),
            "notes": self.notes_input.toPlainText().strip() or None
        }
    
    def validate_data(self):
        """التحقق من صحة البيانات"""
        data = self.get_data()
        
        if data["amount"] <= 0:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال مبلغ صحيح")
            return False
        
        if not data["description"]:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال وصف المعاملة")
            return False
        
        return True
    
    def accept(self):
        """قبول النموذج"""
        if self.validate_data():
            super().accept()


class TransactionsWidget(QWidget):
    """واجهة إدارة المعاملات المالية"""
    
    def __init__(self, user, access_token):
        super().__init__()
        self.user = user
        self.access_token = access_token
        self.transactions_data = []
        
        self.init_ui()
        self.load_transactions()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(15)
        
        # تبويبات
        self.tabs = QTabWidget()
        
        # تبويب المعاملات
        transactions_tab = QWidget()
        self.create_transactions_tab(transactions_tab)
        self.tabs.addTab(transactions_tab, "المعاملات")
        
        # تبويب التقارير
        reports_tab = QWidget()
        self.create_reports_tab(reports_tab)
        self.tabs.addTab(reports_tab, "التقارير المالية")
        
        layout.addWidget(self.tabs)
    
    def create_transactions_tab(self, tab_widget):
        """إنشاء تبويب المعاملات"""
        layout = QVBoxLayout(tab_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(15)
        
        # شريط الأدوات
        self.create_toolbar(layout)
        
        # شريط البحث والفلاتر
        self.create_search_bar(layout)
        
        # جدول المعاملات
        self.create_transactions_table(layout)
        
        # شريط الحالة
        self.create_status_bar(layout)
    
    def create_reports_tab(self, tab_widget):
        """إنشاء تبويب التقارير"""
        layout = QVBoxLayout(tab_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # عنوان
        title_label = QLabel("التقارير المالية")
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #333;")
        layout.addWidget(title_label)
        
        # فترة التقرير
        period_frame = QFrame()
        period_layout = QHBoxLayout(period_frame)
        
        period_layout.addWidget(QLabel("من:"))
        self.start_date_input = QDateEdit()
        self.start_date_input.setDate(QDate.currentDate().addDays(-30))
        self.start_date_input.setCalendarPopup(True)
        period_layout.addWidget(self.start_date_input)
        
        period_layout.addWidget(QLabel("إلى:"))
        self.end_date_input = QDateEdit()
        self.end_date_input.setDate(QDate.currentDate())
        self.end_date_input.setCalendarPopup(True)
        period_layout.addWidget(self.end_date_input)
        
        generate_button = QPushButton("إنشاء تقرير")
        generate_button.clicked.connect(self.generate_financial_report)
        period_layout.addWidget(generate_button)
        
        period_layout.addStretch()
        layout.addWidget(period_frame)
        
        # منطقة عرض التقرير
        self.report_text = QTextEdit()
        self.report_text.setReadOnly(True)
        self.report_text.setPlainText("اختر فترة زمنية واضغط 'إنشاء تقرير' لعرض الملخص المالي")
        layout.addWidget(self.report_text)
    
    def create_toolbar(self, layout):
        """إنشاء شريط الأدوات"""
        toolbar_frame = QFrame()
        toolbar_layout = QHBoxLayout(toolbar_frame)
        toolbar_layout.setContentsMargins(10, 10, 10, 10)
        
        # زر إضافة معاملة جديدة
        add_button = QPushButton("➕ إضافة معاملة جديدة")
        add_button.setObjectName("primaryButton")
        add_button.clicked.connect(self.add_transaction)
        toolbar_layout.addWidget(add_button)
        
        # زر فتح صندوق
        open_box_button = QPushButton("📂 فتح صندوق")
        open_box_button.setObjectName("secondaryButton")
        open_box_button.clicked.connect(self.open_cash_box)
        toolbar_layout.addWidget(open_box_button)
        
        # زر إغلاق صندوق
        close_box_button = QPushButton("📁 إغلاق صندوق")
        close_box_button.setObjectName("warningButton")
        close_box_button.clicked.connect(self.close_cash_box)
        toolbar_layout.addWidget(close_box_button)
        
        # زر تحديث البيانات
        refresh_button = QPushButton("🔄 تحديث")
        refresh_button.clicked.connect(self.load_transactions)
        toolbar_layout.addWidget(refresh_button)
        
        # مساحة فارغة
        toolbar_layout.addStretch()
        
        # زر طباعة
        print_button = QPushButton("🖨️ طباعة")
        print_button.clicked.connect(self.print_list)
        toolbar_layout.addWidget(print_button)
        
        # تطبيق الأنماط
        toolbar_frame.setStyleSheet("""
        QPushButton#primaryButton {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: bold;
        }
        
        QPushButton#primaryButton:hover {
            background: #45a049;
        }
        
        QPushButton#secondaryButton {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: bold;
        }
        
        QPushButton#secondaryButton:hover {
            background: #1976D2;
        }
        
        QPushButton#warningButton {
            background: #FF9800;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: bold;
        }
        
        QPushButton#warningButton:hover {
            background: #F57C00;
        }
        
        QPushButton {
            padding: 8px 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
        }
        
        QPushButton:hover {
            background: #f5f5f5;
        }
        """)
        
        layout.addWidget(toolbar_frame)
    
    def create_search_bar(self, layout):
        """إنشاء شريط البحث"""
        search_frame = QFrame()
        search_layout = QHBoxLayout(search_frame)
        search_layout.setContentsMargins(10, 10, 10, 10)
        
        # فترة زمنية
        date_label = QLabel("من:")
        search_layout.addWidget(date_label)
        
        self.start_date_filter = QDateEdit()
        self.start_date_filter.setDate(QDate.currentDate().addDays(-7))
        self.start_date_filter.setCalendarPopup(True)
        self.start_date_filter.dateChanged.connect(self.filter_transactions)
        search_layout.addWidget(self.start_date_filter)
        
        to_label = QLabel("إلى:")
        search_layout.addWidget(to_label)
        
        self.end_date_filter = QDateEdit()
        self.end_date_filter.setDate(QDate.currentDate())
        self.end_date_filter.setCalendarPopup(True)
        self.end_date_filter.dateChanged.connect(self.filter_transactions)
        search_layout.addWidget(self.end_date_filter)
        
        # فلتر النوع
        type_label = QLabel("النوع:")
        search_layout.addWidget(type_label)
        
        self.type_combo = QComboBox()
        self.type_combo.addItem("الكل")
        self.type_combo.currentTextChanged.connect(self.filter_transactions)
        search_layout.addWidget(self.type_combo)
        
        # فلتر طريقة الدفع
        payment_label = QLabel("طريقة الدفع:")
        search_layout.addWidget(payment_label)
        
        self.payment_combo = QComboBox()
        self.payment_combo.addItems(["الكل", "نقدي", "بطاقة", "تحويل بنكي", "شيك"])
        self.payment_combo.currentTextChanged.connect(self.filter_transactions)
        search_layout.addWidget(self.payment_combo)
        
        layout.addWidget(search_frame)
    
    def create_transactions_table(self, layout):
        """إنشاء جدول المعاملات"""
        self.table = QTableWidget()
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        
        # أعمدة الجدول
        columns = [
            "رقم المعاملة", "النوع", "المبلغ", "طريقة الدفع", 
            "الوصف", "التاريخ", "المستخدم"
        ]
        
        self.table.setColumnCount(len(columns))
        self.table.setHorizontalHeaderLabels(columns)
        
        # تخصيص عرض الأعمدة
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)  # رقم المعاملة
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.Stretch)  # الوصف
        
        # أحداث الجدول
        self.table.doubleClicked.connect(self.view_transaction_details)
        
        layout.addWidget(self.table)
    
    def create_status_bar(self, layout):
        """إنشاء شريط الحالة"""
        status_frame = QFrame()
        status_layout = QHBoxLayout(status_frame)
        
        self.status_label = QLabel("جاري تحميل البيانات...")
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        # إجماليات سريعة
        self.totals_label = QLabel("")
        self.totals_label.setStyleSheet("font-weight: bold; color: #2196F3;")
        status_layout.addWidget(self.totals_label)
        
        status_frame.setStyleSheet("padding: 5px; border-top: 1px solid #ddd;")
        layout.addWidget(status_frame)
    
    def load_transactions(self):
        """تحميل قائمة المعاملات"""
        try:
            self.status_label.setText("جاري تحميل البيانات...")
            
            headers = {"Authorization": f"Bearer {self.access_token}"}
            
            # تحديد الفترة الزمنية
            start_date = self.start_date_filter.date().toString(Qt.DateFormat.ISODate)
            end_date = self.end_date_filter.date().toString(Qt.DateFormat.ISODate)
            
            url = f"http://{settings.api_host}:{settings.api_port}/api/transactions/"
            params = {
                "start_date": start_date,
                "end_date": end_date,
                "limit": 1000
            }
            
            response = requests.get(url, headers=headers, params=params, timeout=10)
            
            if response.status_code == 200:
                self.transactions_data = response.json()
                self.populate_table()
                self.update_type_filter()
                self.calculate_totals()
                self.status_label.setText(f"تم تحميل {len(self.transactions_data)} معاملة")
            else:
                QMessageBox.warning(self, "خطأ", "فشل في تحميل بيانات المعاملات")
                self.status_label.setText("فشل في تحميل البيانات")
                
        except requests.exceptions.ConnectionError:
            QMessageBox.critical(self, "خطأ في الاتصال", "لا يمكن الاتصال بالخادم")
            self.status_label.setText("خطأ في الاتصال")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ غير متوقع: {str(e)}")
            self.status_label.setText("خطأ في التحميل")
    
    def update_type_filter(self):
        """تحديث فلتر الأنواع"""
        types = set()
        for transaction in self.transactions_data:
            types.add(transaction["type"])
        
        current_type = self.type_combo.currentText()
        self.type_combo.clear()
        self.type_combo.addItem("الكل")
        
        for transaction_type in sorted(types):
            self.type_combo.addItem(transaction_type)
        
        # استعادة الاختيار السابق
        index = self.type_combo.findText(current_type)
        if index >= 0:
            self.type_combo.setCurrentIndex(index)
    
    def populate_table(self, data=None):
        """ملء الجدول بالبيانات"""
        if data is None:
            data = self.transactions_data
        
        self.table.setRowCount(len(data))
        
        for row, transaction in enumerate(data):
            # رقم المعاملة
            self.table.setItem(row, 0, QTableWidgetItem(transaction["transaction_number"]))
            
            # النوع
            self.table.setItem(row, 1, QTableWidgetItem(transaction["type"]))
            
            # المبلغ
            amount = f"{transaction['amount']:.2f}"
            amount_item = QTableWidgetItem(amount)
            
            # تلوين المبلغ حسب النوع
            income_types = ["subscription", "renewal", "router_sale", "service_fee"]
            if transaction["type"] in income_types:
                amount_item.setForeground(Qt.GlobalColor.green)
            else:
                amount_item.setForeground(Qt.GlobalColor.red)
            
            self.table.setItem(row, 2, amount_item)
            
            # طريقة الدفع
            payment_map = {
                "cash": "نقدي",
                "card": "بطاقة",
                "bank_transfer": "تحويل بنكي",
                "check": "شيك"
            }
            payment_method = payment_map.get(transaction["payment_method"], transaction["payment_method"])
            self.table.setItem(row, 3, QTableWidgetItem(payment_method))
            
            # الوصف
            description = transaction.get("description", "")
            self.table.setItem(row, 4, QTableWidgetItem(description))
            
            # التاريخ
            transaction_date = transaction["transaction_date"].split("T")[0]
            self.table.setItem(row, 5, QTableWidgetItem(transaction_date))
            
            # المستخدم
            user_name = transaction.get("user_name", "غير محدد")
            self.table.setItem(row, 6, QTableWidgetItem(user_name))
    
    def filter_transactions(self):
        """فلترة المعاملات"""
        type_filter = self.type_combo.currentText()
        payment_filter = self.payment_combo.currentText()
        
        filtered_data = []
        
        for transaction in self.transactions_data:
            # فلتر النوع
            if type_filter != "الكل" and transaction["type"] != type_filter:
                continue
            
            # فلتر طريقة الدفع
            payment_map = {"نقدي": "cash", "بطاقة": "card", "تحويل بنكي": "bank_transfer", "شيك": "check"}
            if payment_filter != "الكل" and transaction["payment_method"] != payment_map.get(payment_filter):
                continue
            
            filtered_data.append(transaction)
        
        self.populate_table(filtered_data)
        self.calculate_totals(filtered_data)
        self.status_label.setText(f"عرض {len(filtered_data)} من {len(self.transactions_data)} معاملة")
    
    def calculate_totals(self, data=None):
        """حساب الإجماليات"""
        if data is None:
            data = self.transactions_data
        
        total_income = 0
        total_expenses = 0
        
        income_types = ["subscription", "renewal", "router_sale", "service_fee"]
        
        for transaction in data:
            if transaction["type"] in income_types:
                total_income += transaction["amount"]
            else:
                total_expenses += transaction["amount"]
        
        net_amount = total_income - total_expenses
        
        self.totals_label.setText(
            f"الدخل: {total_income:.2f} | المصروفات: {total_expenses:.2f} | الصافي: {net_amount:.2f}"
        )
    
    def add_transaction(self):
        """إضافة معاملة جديدة"""
        dialog = AddTransactionDialog(self.access_token, self)
        
        if dialog.exec() == QDialog.DialogCode.Accepted:
            data = dialog.get_data()
            
            try:
                headers = {"Authorization": f"Bearer {self.access_token}"}
                url = f"http://{settings.api_host}:{settings.api_port}/api/transactions/"
                response = requests.post(url, json=data, headers=headers, timeout=10)
                
                if response.status_code == 200:
                    QMessageBox.information(self, "نجح", "تم إضافة المعاملة بنجاح")
                    self.load_transactions()  # تحديث القائمة
                else:
                    error_msg = "فشل في إضافة المعاملة"
                    if response.status_code == 400:
                        error_data = response.json()
                        error_msg = error_data.get("detail", error_msg)
                    QMessageBox.warning(self, "خطأ", error_msg)
                    
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في إضافة المعاملة: {str(e)}")
    
    def view_transaction_details(self):
        """عرض تفاصيل المعاملة"""
        current_row = self.table.currentRow()
        if current_row >= 0:
            transaction_number = self.table.item(current_row, 0).text()
            QMessageBox.information(self, "تفاصيل المعاملة", f"سيتم عرض تفاصيل المعاملة {transaction_number}")
    
    def open_cash_box(self):
        """فتح صندوق جديد"""
        QMessageBox.information(self, "فتح صندوق", "سيتم فتح نافذة فتح صندوق جديد")
    
    def close_cash_box(self):
        """إغلاق الصندوق"""
        QMessageBox.information(self, "إغلاق صندوق", "سيتم فتح نافذة إغلاق الصندوق")
    
    def generate_financial_report(self):
        """إنشاء تقرير مالي"""
        try:
            start_date = self.start_date_input.date().toString(Qt.DateFormat.ISODate)
            end_date = self.end_date_input.date().toString(Qt.DateFormat.ISODate)
            
            headers = {"Authorization": f"Bearer {self.access_token}"}
            url = f"http://{settings.api_host}:{settings.api_port}/api/reports/financial-summary"
            params = {"start_date": start_date, "end_date": end_date}
            
            response = requests.get(url, headers=headers, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                self.display_financial_report(data)
            else:
                QMessageBox.warning(self, "خطأ", "فشل في إنشاء التقرير المالي")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ في إنشاء التقرير: {str(e)}")
    
    def display_financial_report(self, data):
        """عرض التقرير المالي"""
        report_text = f"""
التقرير المالي
================

الفترة: من {data['period']['start_date']} إلى {data['period']['end_date']}
عدد الأيام: {data['period']['days']}

الملخص العام:
--------------
إجمالي الدخل: {data['summary']['total_income']:.2f} USD
إجمالي المصروفات: {data['summary']['total_expenses']:.2f} USD
صافي الربح: {data['summary']['net_profit']:.2f} USD
هامش الربح: {data['summary']['profit_margin']:.1f}%
عدد المعاملات: {data['summary']['transactions_count']}

تفصيل الدخل:
-------------
"""
        
        for income_type, amount in data['income_breakdown'].items():
            report_text += f"{income_type}: {amount:.2f} USD\n"
        
        report_text += "\nتفصيل المصروفات:\n"
        report_text += "----------------\n"
        
        for expense_type, amount in data['expenses_breakdown'].items():
            report_text += f"{expense_type}: {amount:.2f} USD\n"
        
        report_text += "\nطرق الدفع:\n"
        report_text += "----------\n"
        
        for payment_method, amount in data['payment_methods'].items():
            report_text += f"{payment_method}: {amount:.2f} USD\n"
        
        self.report_text.setPlainText(report_text)
    
    def print_list(self):
        """طباعة القائمة"""
        QMessageBox.information(self, "طباعة", "سيتم طباعة قائمة المعاملات")
