"""
نموذج إدارة الكبلات للعمال
"""
from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.database import Base


class Employee(Base):
    """نموذج العامل"""
    __tablename__ = "employees"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    phone = Column(String(20), nullable=True)
    email = Column(String(100), nullable=True)
    address = Column(Text, nullable=True)
    
    # معلومات العمل
    employee_id = Column(String(20), unique=True, nullable=True)
    position = Column(String(50), nullable=True)
    department = Column(String(50), nullable=True)
    hire_date = Column(DateTime(timezone=True), nullable=True)
    
    # حالة العامل
    is_active = Column(Boolean, default=True)
    
    # رصيد الكبلات
    cable_balance = Column(Integer, default=0)  # عدد الكبلات المتاحة
    cable_limit = Column(Integer, default=100)  # الحد الأقصى للكبلات
    
    # معلومات إضافية
    notes = Column(Text, nullable=True)
    
    # تواريخ النظام
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # العلاقات
    cable_assignments = relationship("CableAssignment", back_populates="employee")
    cable_usages = relationship("CableUsage", back_populates="employee")
    
    def __repr__(self):
        return f"<Employee(name='{self.name}', cable_balance={self.cable_balance})>"
    
    @property
    def can_receive_cables(self):
        """التحقق من إمكانية استلام كبلات إضافية"""
        return self.cable_balance < self.cable_limit and self.is_active
    
    @property
    def cables_used_this_week(self):
        """عدد الكبلات المستخدمة هذا الأسبوع"""
        from datetime import datetime, timedelta
        week_start = datetime.now() - timedelta(days=7)
        return sum(
            usage.quantity for usage in self.cable_usages 
            if usage.usage_date >= week_start
        )


class CableType(Base):
    """نموذج نوع الكبل"""
    __tablename__ = "cable_types"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, unique=True)
    description = Column(Text, nullable=True)
    
    # مواصفات الكبل
    length = Column(Integer, nullable=True)  # الطول بالمتر
    color = Column(String(20), nullable=True)
    category = Column(String(20), nullable=True)  # Cat5, Cat6, Fiber, etc.
    
    # معلومات السعر
    cost_price = Column(Float, default=0.0)
    selling_price = Column(Float, default=0.0)
    
    # المخزون
    current_stock = Column(Integer, default=0)
    min_stock = Column(Integer, default=10)
    
    # إعدادات
    is_active = Column(Boolean, default=True)
    
    # تواريخ النظام
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # العلاقات
    assignments = relationship("CableAssignment", back_populates="cable_type")
    usages = relationship("CableUsage", back_populates="cable_type")
    
    def __repr__(self):
        return f"<CableType(name='{self.name}', stock={self.current_stock})>"


class CableAssignment(Base):
    """نموذج تسليم الكبلات للعامل"""
    __tablename__ = "cable_assignments"
    
    id = Column(Integer, primary_key=True, index=True)
    employee_id = Column(Integer, ForeignKey("employees.id"), nullable=False)
    cable_type_id = Column(Integer, ForeignKey("cable_types.id"), nullable=False)
    
    # معلومات التسليم
    quantity = Column(Integer, nullable=False)
    assignment_date = Column(DateTime(timezone=True), server_default=func.now())
    assigned_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # معلومات إضافية
    notes = Column(Text, nullable=True)
    reference_number = Column(String(50), nullable=True)
    
    # تواريخ النظام
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # العلاقات
    employee = relationship("Employee", back_populates="cable_assignments")
    cable_type = relationship("CableType", back_populates="assignments")
    assigner = relationship("User")
    
    def __repr__(self):
        return f"<CableAssignment(employee='{self.employee.name}', quantity={self.quantity})>"


class CableUsage(Base):
    """نموذج استخدام الكبلات"""
    __tablename__ = "cable_usages"
    
    id = Column(Integer, primary_key=True, index=True)
    employee_id = Column(Integer, ForeignKey("employees.id"), nullable=False)
    cable_type_id = Column(Integer, ForeignKey("cable_types.id"), nullable=False)
    
    # معلومات الاستخدام
    quantity = Column(Integer, nullable=False)
    usage_date = Column(DateTime(timezone=True), server_default=func.now())
    
    # ربط مع المشترك أو الراوتر
    subscriber_id = Column(Integer, ForeignKey("subscribers.id"), nullable=True)
    router_id = Column(Integer, ForeignKey("routers.id"), nullable=True)
    
    # نوع الاستخدام
    usage_type = Column(String(20), nullable=False)  # installation, repair, replacement
    
    # معلومات إضافية
    notes = Column(Text, nullable=True)
    location = Column(String(200), nullable=True)
    
    # تواريخ النظام
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # العلاقات
    employee = relationship("Employee", back_populates="cable_usages")
    cable_type = relationship("CableType", back_populates="usages")
    subscriber = relationship("Subscriber")
    router = relationship("Router")
    
    def __repr__(self):
        return f"<CableUsage(employee='{self.employee.name}', quantity={self.quantity}, type='{self.usage_type}')>"


class WeeklyCableReport(Base):
    """نموذج تقرير الكبلات الأسبوعي"""
    __tablename__ = "weekly_cable_reports"
    
    id = Column(Integer, primary_key=True, index=True)
    employee_id = Column(Integer, ForeignKey("employees.id"), nullable=False)
    
    # فترة التقرير
    week_start = Column(DateTime(timezone=True), nullable=False)
    week_end = Column(DateTime(timezone=True), nullable=False)
    
    # إحصائيات الأسبوع
    cables_assigned = Column(Integer, default=0)
    cables_used = Column(Integer, default=0)
    cables_remaining = Column(Integer, default=0)
    
    # معلومات إضافية
    notes = Column(Text, nullable=True)
    generated_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # تواريخ النظام
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # العلاقات
    employee = relationship("Employee")
    generator = relationship("User")
    
    def __repr__(self):
        return f"<WeeklyCableReport(employee='{self.employee.name}', week={self.week_start.date()})>"
