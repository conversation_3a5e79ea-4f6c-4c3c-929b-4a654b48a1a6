"""
نموذج المشتركين
"""
from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.database import Base


class Subscriber(Base):
    """نموذج المشترك"""
    __tablename__ = "subscribers"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    phone = Column(String(20), nullable=False, index=True)
    email = Column(String(100), nullable=True)
    address = Column(Text, nullable=False)
    national_id = Column(String(20), nullable=True, unique=True)
    
    # معلومات الاشتراك
    package_id = Column(Integer, ForeignKey("packages.id"), nullable=True)
    subscription_start = Column(DateTime(timezone=True), nullable=True)
    subscription_end = Column(DateTime(timezone=True), nullable=True)
    is_active = Column(Boolean, default=True)
    
    # معلومات مالية
    balance = Column(Float, default=0.0)  # الرصيد المتبقي
    total_paid = Column(Float, default=0.0)  # إجمالي المدفوع
    
    # معلومات إضافية
    notes = Column(Text, nullable=True)
    installation_date = Column(DateTime(timezone=True), nullable=True)
    technician_notes = Column(Text, nullable=True)
    
    # تواريخ النظام
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    
    # العلاقات
    package = relationship("Package", back_populates="subscribers")
    transactions = relationship("Transaction", back_populates="subscriber")
    creator = relationship("User")
    
    def __repr__(self):
        return f"<Subscriber(name='{self.name}', phone='{self.phone}')>"
    
    @property
    def is_subscription_active(self):
        """التحقق من نشاط الاشتراك"""
        if not self.subscription_end:
            return False
        return self.subscription_end > func.now() and self.is_active
    
    @property
    def days_remaining(self):
        """عدد الأيام المتبقية في الاشتراك"""
        if not self.subscription_end:
            return 0
        delta = self.subscription_end - func.now()
        return max(0, delta.days)
    
    def extend_subscription(self, days: int):
        """تمديد الاشتراك"""
        if self.subscription_end:
            self.subscription_end = self.subscription_end + func.timedelta(days=days)
        else:
            self.subscription_end = func.now() + func.timedelta(days=days)
    
    def add_payment(self, amount: float):
        """إضافة دفعة"""
        self.total_paid += amount
        self.balance += amount
