#!/usr/bin/env python3
"""
خادم أساسي بدون مزامنة
"""
import asyncio
import uvicorn
from app.core.config import create_directories
from app.db.database import init_db

async def setup():
    """إعداد أساسي"""
    print("🚀 إعداد التطبيق...")
    create_directories()
    await init_db()
    print("✅ تم إعداد التطبيق")

def main():
    """تشغيل الخادم"""
    # إعداد التطبيق
    asyncio.run(setup())
    
    print("🌐 تشغيل خادم API...")
    print("📚 الوثائق: http://localhost:8000/docs")
    print("🔗 API: http://localhost:8000")
    
    # استيراد التطبيق هنا لتجنب مشاكل المزامنة
    from app.api.main import app
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info"
    )

if __name__ == "__main__":
    main()
