"""
مسارات إدارة المخزون
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel

from app.db.database import get_db
from app.services.auth import get_current_active_user, require_permission
from app.models.user import User
from app.models.inventory import Category, Unit, Product, StockMovement

router = APIRouter()


class CategoryCreate(BaseModel):
    name: str
    description: Optional[str] = None
    parent_id: Optional[int] = None


class UnitCreate(BaseModel):
    name: str
    symbol: str
    description: Optional[str] = None


class ProductCreate(BaseModel):
    name: str
    code: Optional[str] = None
    barcode: Optional[str] = None
    description: Optional[str] = None
    category_id: Optional[int] = None
    unit_id: int
    cost_price: float = 0.0
    selling_price: float = 0.0
    min_selling_price: float = 0.0
    current_stock: int = 0
    min_stock: int = 0
    max_stock: int = 1000
    is_service: bool = False
    track_stock: bool = True
    supplier: Optional[str] = None
    location: Optional[str] = None
    notes: Optional[str] = None


class StockMovementCreate(BaseModel):
    product_id: int
    movement_type: str  # in, out, adjustment
    quantity: int
    unit_cost: float = 0.0
    reference_number: Optional[str] = None
    reference_type: Optional[str] = None
    notes: Optional[str] = None


@router.get("/categories/")
async def get_categories(
    current_user: User = Depends(require_permission("inventory")),
    db: Session = Depends(get_db)
):
    """الحصول على التصنيفات"""
    categories = db.query(Category).filter(Category.is_active == True).all()
    return categories


@router.post("/categories/")
async def create_category(
    category_data: CategoryCreate,
    current_user: User = Depends(require_permission("inventory")),
    db: Session = Depends(get_db)
):
    """إنشاء تصنيف جديد"""
    category = Category(**category_data.dict())
    db.add(category)
    db.commit()
    db.refresh(category)
    return category


@router.get("/units/")
async def get_units(
    current_user: User = Depends(require_permission("inventory")),
    db: Session = Depends(get_db)
):
    """الحصول على الوحدات"""
    units = db.query(Unit).all()
    return units


@router.post("/units/")
async def create_unit(
    unit_data: UnitCreate,
    current_user: User = Depends(require_permission("inventory")),
    db: Session = Depends(get_db)
):
    """إنشاء وحدة جديدة"""
    # التحقق من عدم وجود الوحدة
    existing_unit = db.query(Unit).filter(Unit.name == unit_data.name).first()
    if existing_unit:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="اسم الوحدة موجود بالفعل"
        )
    
    unit = Unit(**unit_data.dict())
    db.add(unit)
    db.commit()
    db.refresh(unit)
    return unit


@router.get("/products/")
async def get_products(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = Query(None, description="البحث في اسم المنتج"),
    category_id: Optional[int] = Query(None, description="فلترة حسب التصنيف"),
    low_stock: bool = Query(False, description="المنتجات منخفضة المخزون"),
    current_user: User = Depends(require_permission("inventory")),
    db: Session = Depends(get_db)
):
    """الحصول على المنتجات"""
    query = db.query(Product).filter(Product.is_active == True)
    
    if search:
        query = query.filter(Product.name.contains(search))
    
    if category_id:
        query = query.filter(Product.category_id == category_id)
    
    if low_stock:
        query = query.filter(Product.current_stock <= Product.min_stock)
    
    products = query.offset(skip).limit(limit).all()
    
    # إضافة معلومات إضافية
    result = []
    for product in products:
        product_data = {
            "id": product.id,
            "name": product.name,
            "code": product.code,
            "current_stock": product.current_stock,
            "min_stock": product.min_stock,
            "cost_price": product.cost_price,
            "selling_price": product.selling_price,
            "profit_margin": product.profit_margin,
            "stock_value": product.stock_value,
            "is_low_stock": product.is_low_stock,
            "category_name": product.category.name if product.category else None,
            "unit_name": product.unit.name if product.unit else None,
            "created_at": product.created_at
        }
        result.append(product_data)
    
    return result


@router.get("/products/{product_id}")
async def get_product(
    product_id: int,
    current_user: User = Depends(require_permission("inventory")),
    db: Session = Depends(get_db)
):
    """الحصول على منتج محدد"""
    product = db.query(Product).filter(Product.id == product_id).first()
    if not product:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="المنتج غير موجود"
        )
    
    return {
        "id": product.id,
        "name": product.name,
        "code": product.code,
        "barcode": product.barcode,
        "description": product.description,
        "category_id": product.category_id,
        "unit_id": product.unit_id,
        "cost_price": product.cost_price,
        "selling_price": product.selling_price,
        "min_selling_price": product.min_selling_price,
        "current_stock": product.current_stock,
        "min_stock": product.min_stock,
        "max_stock": product.max_stock,
        "is_service": product.is_service,
        "track_stock": product.track_stock,
        "supplier": product.supplier,
        "location": product.location,
        "notes": product.notes,
        "profit_margin": product.profit_margin,
        "stock_value": product.stock_value,
        "is_low_stock": product.is_low_stock,
        "category_name": product.category.name if product.category else None,
        "unit_name": product.unit.name if product.unit else None,
        "created_at": product.created_at
    }


@router.post("/products/")
async def create_product(
    product_data: ProductCreate,
    current_user: User = Depends(require_permission("inventory")),
    db: Session = Depends(get_db)
):
    """إنشاء منتج جديد"""
    # التحقق من عدم وجود الكود
    if product_data.code:
        existing_code = db.query(Product).filter(Product.code == product_data.code).first()
        if existing_code:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="كود المنتج موجود بالفعل"
            )
    
    # التحقق من الباركود
    if product_data.barcode:
        existing_barcode = db.query(Product).filter(Product.barcode == product_data.barcode).first()
        if existing_barcode:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="الباركود موجود بالفعل"
            )
    
    product = Product(**product_data.dict())
    db.add(product)
    db.commit()
    db.refresh(product)
    
    # إنشاء حركة مخزون أولية إذا كان هناك مخزون
    if product.current_stock > 0:
        stock_movement = StockMovement(
            product_id=product.id,
            movement_type="in",
            quantity=product.current_stock,
            unit_cost=product.cost_price,
            reference_type="initial_stock",
            notes="المخزون الأولي",
            stock_before=0,
            stock_after=product.current_stock,
            user_id=current_user.id
        )
        db.add(stock_movement)
        db.commit()
    
    return product


@router.post("/stock-movements/")
async def create_stock_movement(
    movement_data: StockMovementCreate,
    current_user: User = Depends(require_permission("inventory")),
    db: Session = Depends(get_db)
):
    """إنشاء حركة مخزون"""
    product = db.query(Product).filter(Product.id == movement_data.product_id).first()
    if not product:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="المنتج غير موجود"
        )
    
    # حفظ المخزون قبل التعديل
    stock_before = product.current_stock
    
    # تحديث المخزون
    if movement_data.movement_type == "in":
        product.current_stock += movement_data.quantity
    elif movement_data.movement_type == "out":
        if product.current_stock < movement_data.quantity:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="المخزون غير كافي"
            )
        product.current_stock -= movement_data.quantity
    elif movement_data.movement_type == "adjustment":
        product.current_stock = movement_data.quantity
    
    # إنشاء حركة المخزون
    stock_movement = StockMovement(
        product_id=movement_data.product_id,
        movement_type=movement_data.movement_type,
        quantity=movement_data.quantity,
        unit_cost=movement_data.unit_cost,
        reference_number=movement_data.reference_number,
        reference_type=movement_data.reference_type,
        notes=movement_data.notes,
        stock_before=stock_before,
        stock_after=product.current_stock,
        user_id=current_user.id
    )
    
    db.add(stock_movement)
    db.commit()
    db.refresh(stock_movement)
    
    return {
        "message": "تم تسجيل حركة المخزون بنجاح",
        "movement_id": stock_movement.id,
        "product_name": product.name,
        "stock_before": stock_before,
        "stock_after": product.current_stock
    }


@router.get("/stock-movements/")
async def get_stock_movements(
    skip: int = 0,
    limit: int = 100,
    product_id: Optional[int] = Query(None, description="فلترة حسب المنتج"),
    movement_type: Optional[str] = Query(None, description="نوع الحركة"),
    current_user: User = Depends(require_permission("inventory")),
    db: Session = Depends(get_db)
):
    """الحصول على حركات المخزون"""
    query = db.query(StockMovement)
    
    if product_id:
        query = query.filter(StockMovement.product_id == product_id)
    
    if movement_type:
        query = query.filter(StockMovement.movement_type == movement_type)
    
    movements = query.order_by(StockMovement.movement_date.desc()).offset(skip).limit(limit).all()
    
    result = []
    for movement in movements:
        movement_data = {
            "id": movement.id,
            "product_name": movement.product.name,
            "movement_type": movement.movement_type,
            "quantity": movement.quantity,
            "unit_cost": movement.unit_cost,
            "stock_before": movement.stock_before,
            "stock_after": movement.stock_after,
            "reference_number": movement.reference_number,
            "reference_type": movement.reference_type,
            "notes": movement.notes,
            "user_name": movement.user.full_name,
            "movement_date": movement.movement_date,
            "created_at": movement.created_at
        }
        result.append(movement_data)
    
    return result


@router.get("/low-stock/")
async def get_low_stock_products(
    current_user: User = Depends(require_permission("inventory")),
    db: Session = Depends(get_db)
):
    """الحصول على المنتجات منخفضة المخزون"""
    products = db.query(Product).filter(
        Product.current_stock <= Product.min_stock,
        Product.is_active == True,
        Product.track_stock == True
    ).all()
    
    result = []
    for product in products:
        result.append({
            "id": product.id,
            "name": product.name,
            "current_stock": product.current_stock,
            "min_stock": product.min_stock,
            "shortage": product.min_stock - product.current_stock,
            "category_name": product.category.name if product.category else None
        })
    
    return result
