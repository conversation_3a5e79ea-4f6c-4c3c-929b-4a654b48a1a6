# دليل التطوير

## هيكل المشروع

```
الشامل/
├── app/                    # التطبيق الرئيسي
│   ├── api/               # API endpoints
│   ├── core/              # الإعدادات الأساسية
│   ├── db/                # قاعدة البيانات
│   ├── models/            # نماذج البيانات
│   └── services/          # الخدمات
├── gui/                   # واجهة المستخدم
│   ├── windows/           # النوافذ الرئيسية
│   └── widgets/           # العناصر المخصصة
├── printing/              # نظام الطباعة
├── sync/                  # نظام المزامنة
├── config/                # ملفات الإعداد
├── data/                  # قاعدة البيانات
├── logs/                  # ملفات السجلات
└── static/                # الملفات الثابتة
```

## إعداد بيئة التطوير

### 1. استنساخ المشروع
```bash
git clone <repository-url>
cd الشامل
```

### 2. إنشاء بيئة افتراضية
```bash
python -m venv venv
source venv/bin/activate  # Linux/macOS
venv\Scripts\activate     # Windows
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
pip install -e .  # تثبيت في وضع التطوير
```

### 4. إعداد الإعدادات
```bash
cp config/.env.example config/.env
# عدل الإعدادات حسب الحاجة
```

### 5. تشغيل الاختبارات
```bash
pytest
```

## أدوات التطوير

### تنسيق الكود
```bash
# تنسيق تلقائي
black .

# فحص جودة الكود
flake8 .

# فحص الأنواع
mypy app/
```

### تشغيل الخادم في وضع التطوير
```bash
# مع إعادة التحميل التلقائي
uvicorn app.api.main:app --reload --host 0.0.0.0 --port 8000
```

### تشغيل الواجهة في وضع التطوير
```bash
python run_gui.py
```

## إضافة ميزات جديدة

### 1. إضافة نموذج جديد
```python
# في app/models/new_model.py
from sqlalchemy import Column, Integer, String
from app.db.database import Base

class NewModel(Base):
    __tablename__ = "new_table"
    
    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False)
```

### 2. إضافة API endpoint
```python
# في app/api/routes/new_route.py
from fastapi import APIRouter, Depends
from app.services.auth import get_current_user

router = APIRouter()

@router.get("/")
async def get_items(current_user = Depends(get_current_user)):
    return {"items": []}
```

### 3. إضافة واجهة جديدة
```python
# في gui/widgets/new_widget.py
from PyQt6.QtWidgets import QWidget, QVBoxLayout

class NewWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        layout = QVBoxLayout(self)
        # إضافة العناصر
```

## قاعدة البيانات

### إنشاء migration جديد
```bash
# إنشاء migration
alembic revision --autogenerate -m "وصف التغيير"

# تطبيق migrations
alembic upgrade head
```

### إعادة تعيين قاعدة البيانات
```bash
rm data/accounting.db
python -c "from app.db.database import init_db; import asyncio; asyncio.run(init_db())"
```

## الاختبارات

### تشغيل جميع الاختبارات
```bash
pytest
```

### تشغيل اختبارات محددة
```bash
pytest tests/test_api.py
pytest tests/test_models.py -v
```

### تشغيل مع تغطية الكود
```bash
pytest --cov=app tests/
```

## التوثيق

### إنشاء وثائق API
```bash
# الوثائق متاحة تلقائياً في
http://localhost:8000/docs
```

### إضافة وثائق للكود
```python
def function_name(param: str) -> str:
    """
    وصف الدالة
    
    Args:
        param: وصف المعامل
    
    Returns:
        وصف القيمة المرجعة
    """
    return param
```

## نصائح التطوير

### 1. استخدام المتغيرات البيئية
```python
from app.core.config import settings

# بدلاً من القيم المباشرة
database_url = settings.database_url
```

### 2. معالجة الأخطاء
```python
try:
    # كود قد يفشل
    result = risky_operation()
except SpecificException as e:
    logger.error(f"خطأ محدد: {e}")
    raise HTTPException(status_code=400, detail="رسالة خطأ واضحة")
```

### 3. استخدام async/await
```python
async def async_function():
    result = await async_operation()
    return result
```

### 4. التحقق من الصلاحيات
```python
@router.get("/protected")
async def protected_route(
    current_user: User = Depends(require_permission("resource"))
):
    return {"data": "محمي"}
```

## نشر التطبيق

### 1. إنشاء ملف executable
```bash
# باستخدام PyInstaller
pip install pyinstaller
pyinstaller --onefile --windowed main.py
```

### 2. إنشاء حزمة للتوزيع
```bash
python setup.py sdist bdist_wheel
```

### 3. نشر على خادم
```bash
# نسخ الملفات
scp -r . user@server:/path/to/app/

# تثبيت المتطلبات على الخادم
pip install -r requirements.txt

# تشغيل الخادم
python run_server.py
```

## استكشاف الأخطاء

### مشاكل شائعة

#### خطأ في قاعدة البيانات
```bash
# حذف وإعادة إنشاء
rm data/accounting.db
python main.py
```

#### خطأ في المتطلبات
```bash
# إعادة تثبيت
pip install -r requirements.txt --force-reinstall
```

#### خطأ في الواجهة
```bash
# تحقق من PyQt6
python -c "import PyQt6; print('PyQt6 installed')"
```

### سجلات الأخطاء
- تحقق من ملفات السجلات في مجلد `logs/`
- استخدم `print()` أو `logger` لتتبع المشاكل

## المساهمة

### 1. إنشاء فرع جديد
```bash
git checkout -b feature/new-feature
```

### 2. إجراء التغييرات
- اتبع معايير الكود
- أضف اختبارات للميزات الجديدة
- حدث الوثائق

### 3. إرسال Pull Request
- اكتب وصف واضح للتغييرات
- تأكد من نجاح جميع الاختبارات
- اطلب مراجعة الكود

## الأمان

### أفضل الممارسات
- لا تحفظ كلمات المرور في الكود
- استخدم متغيرات البيئة للمعلومات الحساسة
- تحقق من صحة جميع المدخلات
- استخدم HTTPS في الإنتاج

### فحص الأمان
```bash
# فحص الثغرات الأمنية
pip install safety
safety check

# فحص الكود
bandit -r app/
```
