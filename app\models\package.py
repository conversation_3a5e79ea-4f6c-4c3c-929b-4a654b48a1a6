"""
نموذج الباقات
"""
from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.database import Base


class Package(Base):
    """نموذج الباقة"""
    __tablename__ = "packages"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    description = Column(Text, nullable=True)
    
    # مواصفات الباقة
    speed = Column(Integer, nullable=False)  # السرعة بالميجا
    price = Column(Float, nullable=False)  # السعر
    type = Column(String(20), nullable=False, default="monthly")  # monthly, yearly, daily
    duration_days = Column(Integer, default=30)  # مدة الباقة بالأيام
    
    # إعدادات إضافية
    data_limit = Column(Integer, nullable=True)  # حد البيانات (GB)
    is_unlimited = Column(Boolean, default=True)  # باقة غير محدودة
    is_active = Column(Boolean, default=True)
    
    # معلومات التكلفة
    cost_price = Column(Float, default=0.0)  # سعر التكلفة
    profit_margin = Column(Float, default=0.0)  # هامش الربح
    
    # تواريخ النظام
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # العلاقات
    subscribers = relationship("Subscriber", back_populates="package")
    
    def __repr__(self):
        return f"<Package(name='{self.name}', speed={self.speed}MB, price={self.price})>"
    
    @property
    def profit(self):
        """حساب الربح"""
        return self.price - self.cost_price
    
    @property
    def profit_percentage(self):
        """نسبة الربح"""
        if self.cost_price == 0:
            return 0
        return (self.profit / self.cost_price) * 100
    
    def calculate_price_for_days(self, days: int) -> float:
        """حساب السعر لعدد أيام محدد"""
        if self.type == "daily":
            return self.price * days
        elif self.type == "monthly":
            return (self.price / 30) * days
        elif self.type == "yearly":
            return (self.price / 365) * days
        return self.price
