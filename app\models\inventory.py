"""
نماذج الجرد والمخزون
"""
from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.database import Base


class Category(Base):
    """نموذج التصنيف"""
    __tablename__ = "categories"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    description = Column(Text, nullable=True)
    parent_id = Column(Integer, ForeignKey("categories.id"), nullable=True)
    is_active = Column(Boolean, default=True)
    
    # تواريخ النظام
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # العلاقات
    parent = relationship("Category", remote_side=[id])
    children = relationship("Category")
    products = relationship("Product", back_populates="category")
    
    def __repr__(self):
        return f"<Category(name='{self.name}')>"


class Unit(Base):
    """نموذج الوحدة"""
    __tablename__ = "units"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(50), nullable=False, unique=True)
    symbol = Column(String(10), nullable=False)
    description = Column(Text, nullable=True)
    
    # تواريخ النظام
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # العلاقات
    products = relationship("Product", back_populates="unit")
    
    def __repr__(self):
        return f"<Unit(name='{self.name}', symbol='{self.symbol}')>"


class Product(Base):
    """نموذج المنتج"""
    __tablename__ = "products"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False, index=True)
    code = Column(String(50), unique=True, nullable=True, index=True)
    barcode = Column(String(50), unique=True, nullable=True)
    description = Column(Text, nullable=True)
    
    # التصنيف والوحدة
    category_id = Column(Integer, ForeignKey("categories.id"), nullable=True)
    unit_id = Column(Integer, ForeignKey("units.id"), nullable=False)
    
    # معلومات السعر
    cost_price = Column(Float, default=0.0)
    selling_price = Column(Float, default=0.0)
    min_selling_price = Column(Float, default=0.0)
    
    # معلومات المخزون
    current_stock = Column(Integer, default=0)
    min_stock = Column(Integer, default=0)
    max_stock = Column(Integer, default=1000)
    
    # إعدادات
    is_active = Column(Boolean, default=True)
    is_service = Column(Boolean, default=False)  # خدمة أم منتج
    track_stock = Column(Boolean, default=True)  # تتبع المخزون
    
    # معلومات إضافية
    supplier = Column(String(100), nullable=True)
    location = Column(String(100), nullable=True)
    notes = Column(Text, nullable=True)
    
    # تواريخ النظام
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # العلاقات
    category = relationship("Category", back_populates="products")
    unit = relationship("Unit", back_populates="products")
    stock_movements = relationship("StockMovement", back_populates="product")
    
    def __repr__(self):
        return f"<Product(name='{self.name}', stock={self.current_stock})>"
    
    @property
    def profit_margin(self):
        """هامش الربح"""
        if self.cost_price == 0:
            return 0
        return ((self.selling_price - self.cost_price) / self.cost_price) * 100
    
    @property
    def is_low_stock(self):
        """التحقق من انخفاض المخزون"""
        return self.current_stock <= self.min_stock
    
    @property
    def stock_value(self):
        """قيمة المخزون"""
        return self.current_stock * self.cost_price


class StockMovement(Base):
    """نموذج حركة المخزون"""
    __tablename__ = "stock_movements"
    
    id = Column(Integer, primary_key=True, index=True)
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False)
    
    # نوع الحركة
    movement_type = Column(String(20), nullable=False)  # in, out, adjustment, transfer
    quantity = Column(Integer, nullable=False)
    unit_cost = Column(Float, default=0.0)
    
    # معلومات الحركة
    reference_number = Column(String(50), nullable=True)
    reference_type = Column(String(20), nullable=True)  # purchase, sale, adjustment
    notes = Column(Text, nullable=True)
    
    # المخزون قبل وبعد
    stock_before = Column(Integer, nullable=False)
    stock_after = Column(Integer, nullable=False)
    
    # معلومات المستخدم
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # تواريخ النظام
    movement_date = Column(DateTime(timezone=True), server_default=func.now())
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # العلاقات
    product = relationship("Product", back_populates="stock_movements")
    user = relationship("User")
    
    def __repr__(self):
        return f"<StockMovement(product='{self.product.name}', type='{self.movement_type}', qty={self.quantity})>"
