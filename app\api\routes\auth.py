"""
مسارات المصادقة
"""
from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from pydantic import BaseModel

from app.db.database import get_db
from app.services.auth import AuthService, get_current_active_user
from app.models.user import User

router = APIRouter()


class LoginResponse(BaseModel):
    """استجابة تسجيل الدخول"""
    access_token: str
    token_type: str
    user: dict


class ChangePasswordRequest(BaseModel):
    """طلب تغيير كلمة المرور"""
    old_password: str
    new_password: str


class ResetPasswordRequest(BaseModel):
    """طلب إعادة تعيين كلمة المرور"""
    username: str
    new_password: str


@router.post("/login", response_model=LoginResponse)
async def login(
    form_data: OAuth2PasswordRequestForm = Depends(),
    db: Session = Depends(get_db)
):
    """تسجيل الدخول"""
    auth_service = AuthService(db)
    try:
        result = auth_service.login(form_data.username, form_data.password)
        return result
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ في تسجيل الدخول"
        )


@router.post("/logout")
async def logout(current_user: User = Depends(get_current_active_user)):
    """تسجيل الخروج"""
    # في JWT، تسجيل الخروج يتم من جانب العميل بحذف الرمز المميز
    return {"message": "تم تسجيل الخروج بنجاح"}


@router.get("/me")
async def get_current_user_info(current_user: User = Depends(get_current_active_user)):
    """الحصول على معلومات المستخدم الحالي"""
    return {
        "id": current_user.id,
        "username": current_user.username,
        "email": current_user.email,
        "full_name": current_user.full_name,
        "role": current_user.role,
        "is_active": current_user.is_active,
        "phone": current_user.phone,
        "last_login": current_user.last_login,
        "created_at": current_user.created_at
    }


@router.post("/change-password")
async def change_password(
    request: ChangePasswordRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """تغيير كلمة المرور"""
    auth_service = AuthService(db)
    try:
        auth_service.change_password(
            current_user, 
            request.old_password, 
            request.new_password
        )
        return {"message": "تم تغيير كلمة المرور بنجاح"}
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ في تغيير كلمة المرور"
        )


@router.post("/reset-password")
async def reset_password(
    request: ResetPasswordRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """إعادة تعيين كلمة المرور (للمدير فقط)"""
    if not current_user.is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="غير مسموح - مطلوب صلاحيات مدير"
        )
    
    auth_service = AuthService(db)
    try:
        auth_service.reset_password(request.username, request.new_password)
        return {"message": "تم إعادة تعيين كلمة المرور بنجاح"}
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="خطأ في إعادة تعيين كلمة المرور"
        )


@router.get("/permissions")
async def get_user_permissions(current_user: User = Depends(get_current_active_user)):
    """الحصول على صلاحيات المستخدم"""
    permissions = []
    
    # قائمة الموارد المتاحة
    resources = [
        "subscribers", "routers", "packages", "transactions",
        "inventory", "cables", "reports", "users", "settings",
        "daily_reports", "monthly_reports", "print_receipts",
        "sales", "purchases", "expenses", "cash_box"
    ]
    
    for resource in resources:
        if current_user.can_access(resource):
            permissions.append(resource)
    
    return {
        "user_id": current_user.id,
        "role": current_user.role,
        "permissions": permissions,
        "is_admin": current_user.is_admin
    }
