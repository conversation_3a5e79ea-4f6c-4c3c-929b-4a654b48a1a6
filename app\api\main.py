"""
التطبيق الرئيسي لـ FastAPI
"""
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import os

from app.core.config import settings
from app.api.routes import auth, users, subscribers, packages, routers, transactions, inventory, cables, reports

# إنشاء تطبيق FastAPI
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="تطبيق محاسبة متكامل لإدارة المشتركين والعمليات المحاسبية",
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None
)

# إعداد CORS
if settings.enable_cors:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # في الإنتاج، حدد النطاقات المسموحة
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

# تضمين المسارات
app.include_router(auth.router, prefix="/api/auth", tags=["المصادقة"])
app.include_router(users.router, prefix="/api/users", tags=["المستخدمين"])
app.include_router(subscribers.router, prefix="/api/subscribers", tags=["المشتركين"])
app.include_router(packages.router, prefix="/api/packages", tags=["الباقات"])
app.include_router(routers.router, prefix="/api/routers", tags=["الراوترات"])
app.include_router(transactions.router, prefix="/api/transactions", tags=["المعاملات"])
app.include_router(inventory.router, prefix="/api/inventory", tags=["المخزون"])
app.include_router(cables.router, prefix="/api/cables", tags=["الكبلات"])
app.include_router(reports.router, prefix="/api/reports", tags=["التقارير"])

# خدمة الملفات الثابتة
if os.path.exists("static"):
    app.mount("/static", StaticFiles(directory="static"), name="static")

# الصفحة الرئيسية
@app.get("/", response_class=HTMLResponse)
async def root():
    """الصفحة الرئيسية"""
    return """
    <!DOCTYPE html>
    <html dir="rtl" lang="ar">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تطبيق المحاسبة المتكامل</title>
        <style>
            body {
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                margin: 0;
                padding: 20px;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                text-align: center;
                min-height: 100vh;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
            }
            .container {
                background: rgba(255, 255, 255, 0.1);
                padding: 40px;
                border-radius: 20px;
                backdrop-filter: blur(10px);
                box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
                border: 1px solid rgba(255, 255, 255, 0.18);
                max-width: 600px;
            }
            h1 {
                font-size: 2.5em;
                margin-bottom: 20px;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            }
            p {
                font-size: 1.2em;
                margin-bottom: 30px;
                line-height: 1.6;
            }
            .links {
                display: flex;
                gap: 20px;
                justify-content: center;
                flex-wrap: wrap;
            }
            .link {
                background: rgba(255, 255, 255, 0.2);
                padding: 15px 30px;
                border-radius: 10px;
                text-decoration: none;
                color: white;
                font-weight: bold;
                transition: all 0.3s ease;
                border: 1px solid rgba(255, 255, 255, 0.3);
            }
            .link:hover {
                background: rgba(255, 255, 255, 0.3);
                transform: translateY(-2px);
                box-shadow: 0 4px 16px rgba(0,0,0,0.2);
            }
            .status {
                margin-top: 30px;
                padding: 15px;
                background: rgba(0, 255, 0, 0.2);
                border-radius: 10px;
                border: 1px solid rgba(0, 255, 0, 0.3);
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🏢 تطبيق المحاسبة المتكامل</h1>
            <p>نظام شامل لإدارة المشتركين والعمليات المحاسبية مع دعم العمل المتزامن عبر الشبكة</p>
            
            <div class="links">
                <a href="/docs" class="link">📚 وثائق API</a>
                <a href="/redoc" class="link">📖 ReDoc</a>
            </div>
            
            <div class="status">
                ✅ الخادم يعمل بنجاح
            </div>
        </div>
    </body>
    </html>
    """

# معالج الأخطاء العام
@app.exception_handler(404)
async def not_found_handler(request, exc):
    return {"error": "الصفحة غير موجودة", "status_code": 404}

@app.exception_handler(500)
async def internal_error_handler(request, exc):
    return {"error": "خطأ داخلي في الخادم", "status_code": 500}

# معلومات الصحة
@app.get("/health")
async def health_check():
    """فحص صحة الخادم"""
    return {
        "status": "healthy",
        "app_name": settings.app_name,
        "version": settings.app_version,
        "timestamp": "2025-01-09T19:00:00Z"
    }
