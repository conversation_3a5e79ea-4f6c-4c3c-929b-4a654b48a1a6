# 📖 دليل الاستخدام - تطبيق المحاسبة المتكامل

## 🚀 التشغيل السريع

### 🔧 الإعداد الأولي (مرة واحدة فقط)
1. **انقر مرتين على**: `install_requirements.bat`
2. **انتظر** حتى انتهاء تثبيت جميع المتطلبات
3. **اتبع** الخطوات أدناه للتشغيل

---

## 🎯 طرق التشغيل

### 📱 الطريقة الأولى: التشغيل المنفصل (الأفضل)

#### 1️⃣ تشغيل خادم API:
- **انقر مرتين على**: `start_api.bat`
- **انتظر** حتى ظهور رسالة "خادم API جاهز!"
- **اتركه يعمل** (لا تغلق النافذة)

#### 2️⃣ تشغيل واجهة سطح المكتب:
- **افتح نافذة جديدة**
- **انقر مرتين على**: `start_gui.bat`
- **ستظهر** نافذة تسجيل الدخول

#### 3️⃣ تسجيل الدخول:
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`
- **اضغط**: "تسجيل الدخول"

---

### 🌐 الطريقة الثانية: استخدام المتصفح فقط

#### 1️⃣ تشغيل الخادم:
- **انقر مرتين على**: `start_api.bat`

#### 2️⃣ فتح المتصفح:
- **اذهب إلى**: http://localhost:8000/docs
- **استخدم** واجهة API التفاعلية

---

### 🔄 الطريقة الثالثة: التشغيل الكامل

#### 1️⃣ التشغيل التلقائي:
- **انقر مرتين على**: `start_venv.bat`
- **سيتم تشغيل** الخادم والواجهة معاً

---

## 🔍 استكشاف الأخطاء

### ❌ "لا تظهر نافذة تسجيل الدخول"

#### الحل:
1. **تأكد** من تشغيل خادم API أولاً
2. **انتظر** 10-15 ثانية بعد تشغيل الخادم
3. **ثم شغل** واجهة سطح المكتب

### ❌ "خطأ في الاتصال بالخادم"

#### الحل:
1. **تأكد** من أن خادم API يعمل
2. **تحقق** من http://localhost:8000/docs في المتصفح
3. **إذا لم يعمل**، أعد تشغيل `start_api.bat`

### ❌ "مكتبة مفقودة"

#### الحل:
1. **شغل**: `install_requirements.bat`
2. **انتظر** حتى انتهاء التثبيت
3. **أعد المحاولة**

### ❌ "المنفذ 8000 مشغول"

#### الحل:
1. **أغلق** جميع نوافذ التطبيق
2. **انتظر** 30 ثانية
3. **أعد التشغيل**

---

## 🎮 كيفية الاستخدام

### 🔑 تسجيل الدخول
- **البيانات الافتراضية**:
  - اسم المستخدم: `admin`
  - كلمة المرور: `admin123`

### 🏠 الشاشة الرئيسية
بعد تسجيل الدخول ستظهر:
- **لوحة التحكم**: إحصائيات عامة
- **المشتركين**: إدارة المشتركين
- **الراوترات**: إدارة الراوترات
- **المعاملات**: إدارة المعاملات المالية
- **التقارير**: تقارير مختلفة

### 📊 الوظائف الرئيسية

#### 👥 إدارة المشتركين:
- **إضافة** مشترك جديد
- **تعديل** بيانات المشترك
- **حذف** مشترك
- **البحث** في المشتركين

#### 🌐 إدارة الراوترات:
- **إضافة** راوتر جديد
- **تعديل** إعدادات الراوتر
- **مراقبة** حالة الراوترات

#### 💰 إدارة المعاملات:
- **تسجيل** دفعة جديدة
- **عرض** تاريخ المدفوعات
- **طباعة** إيصالات

#### 📈 التقارير:
- **تقرير** المبيعات
- **تقرير** المشتركين
- **تقرير** الأرباح

---

## 🌐 الوصول عبر الشبكة

### 📡 للوصول من أجهزة أخرى:
1. **شغل الخادم** على الجهاز الرئيسي
2. **اعرف عنوان IP** للجهاز الرئيسي
3. **من الأجهزة الأخرى** اذهب إلى: `http://IP:8000/docs`

### 🔒 فتح المنفذ في الجدار الناري:
1. **افتح** إعدادات Windows Firewall
2. **أضف** استثناء للمنفذ 8000
3. **أو** أضف استثناء لـ Python

---

## 📞 الحصول على المساعدة

### 🔍 فحص النظام:
```bash
python run_robust.py
# اختر: 4 (فحص النظام)
```

### 📋 معلومات مفيدة:
- **مجلد البيانات**: `data/`
- **قاعدة البيانات**: `data/accounting.db`
- **ملفات الإعداد**: `.env`
- **السجلات**: `logs/`

---

## 🎯 نصائح للاستخدام الأمثل

### ⚡ للأداء الأفضل:
1. **شغل** كمدير (Run as Administrator)
2. **أضف** مجلد التطبيق لاستثناءات مكافح الفيروسات
3. **استخدم** SSD إذا متوفر

### 🔒 للأمان:
1. **غير** كلمة مرور admin
2. **فعّل** النسخ الاحتياطي
3. **احتفظ** بنسخة من مجلد `data/`

### 🌐 للعمل عبر الشبكة:
1. **شغل الخادم** على جهاز مركزي
2. **استخدم** واجهة الويب من الأجهزة الأخرى
3. **تأكد** من استقرار الشبكة

---

## 🎊 مبروك!

**التطبيق جاهز للاستخدام في جميع الحالات!**

- ✅ يعمل مع أو بدون إنترنت
- ✅ يعمل مع البروكسي/VPN
- ✅ واجهة عربية كاملة
- ✅ نظام أمان متقدم
- ✅ تقارير احترافية
