"""
نافذة تسجيل الدخول
"""
import sys
import requests
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
    QPushButton, QMessageBox, QFrame, QApplication
)
from PyQt6.QtCore import Qt, pyqtSignal, QThread, pyqtSlot, QTimer
from PyQt6.QtGui import QFont, QPixmap, QPalette, QColor

from app.core.config import settings


class LoginThread(QThread):
    """خيط تسجيل الدخول"""
    login_success = pyqtSignal(dict)
    login_failed = pyqtSignal(str)
    
    def __init__(self, username, password):
        super().__init__()
        self.username = username
        self.password = password
    
    def run(self):
        try:
            # إرسال طلب تسجيل الدخول
            url = f"http://{settings.api_host}:{settings.api_port}/api/auth/login"
            data = {
                "username": self.username,
                "password": self.password
            }
            
            response = requests.post(url, data=data, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                self.login_success.emit(result)
            else:
                error_msg = "اسم المستخدم أو كلمة المرور غير صحيحة"
                if response.status_code == 422:
                    error_msg = "بيانات غير صالحة"
                elif response.status_code == 500:
                    error_msg = "خطأ في الخادم"
                self.login_failed.emit(error_msg)
                
        except requests.exceptions.ConnectionError:
            self.login_failed.emit("لا يمكن الاتصال بالخادم")
        except requests.exceptions.Timeout:
            self.login_failed.emit("انتهت مهلة الاتصال")
        except Exception as e:
            self.login_failed.emit(f"خطأ غير متوقع: {str(e)}")


class LoginWindow(QWidget):
    """نافذة تسجيل الدخول"""
    login_successful = pyqtSignal(dict)
    login_cancelled = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.login_thread = None
        self.init_ui()
        self.apply_styles()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        self.setWindowTitle("تسجيل الدخول - تطبيق المحاسبة المتكامل")
        self.setFixedSize(400, 500)
        self.setWindowFlags(Qt.WindowType.FramelessWindowHint)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # إطار النافذة
        frame = QFrame()
        frame.setObjectName("loginFrame")
        frame_layout = QVBoxLayout(frame)
        frame_layout.setContentsMargins(40, 40, 40, 40)
        frame_layout.setSpacing(20)
        
        # الشعار والعنوان
        self.create_header(frame_layout)
        
        # حقول الإدخال
        self.create_input_fields(frame_layout)
        
        # أزرار التحكم
        self.create_buttons(frame_layout)
        
        # معلومات إضافية
        self.create_footer(frame_layout)
        
        main_layout.addWidget(frame)
        self.setLayout(main_layout)

        # تركيز على حقل اسم المستخدم (بعد إنشاء جميع العناصر)
        QTimer.singleShot(100, self.username_input.setFocus)
    
    def create_header(self, layout):
        """إنشاء رأس النافذة"""
        # العنوان الرئيسي
        title_label = QLabel("تطبيق المحاسبة المتكامل")
        title_label.setObjectName("titleLabel")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)
        
        # العنوان الفرعي
        subtitle_label = QLabel("نظام شامل لإدارة المشتركين والعمليات المحاسبية")
        subtitle_label.setObjectName("subtitleLabel")
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle_label.setWordWrap(True)
        layout.addWidget(subtitle_label)
        
        # خط فاصل
        line = QFrame()
        line.setFrameShape(QFrame.Shape.HLine)
        line.setFrameShadow(QFrame.Shadow.Sunken)
        line.setObjectName("separatorLine")
        layout.addWidget(line)
    
    def create_input_fields(self, layout):
        """إنشاء حقول الإدخال"""
        # اسم المستخدم
        username_label = QLabel("اسم المستخدم:")
        username_label.setObjectName("fieldLabel")
        layout.addWidget(username_label)
        
        self.username_input = QLineEdit()
        self.username_input.setObjectName("inputField")
        self.username_input.setPlaceholderText("أدخل اسم المستخدم")
        layout.addWidget(self.username_input)

        # كلمة المرور
        password_label = QLabel("كلمة المرور:")
        password_label.setObjectName("fieldLabel")
        layout.addWidget(password_label)

        self.password_input = QLineEdit()
        self.password_input.setObjectName("inputField")
        self.password_input.setPlaceholderText("أدخل كلمة المرور")
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.returnPressed.connect(self.login)
        layout.addWidget(self.password_input)

        # ربط Enter في اسم المستخدم بكلمة المرور
        self.username_input.returnPressed.connect(self.password_input.setFocus)
    
    def create_buttons(self, layout):
        """إنشاء الأزرار"""
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)
        
        # زر تسجيل الدخول
        self.login_button = QPushButton("تسجيل الدخول")
        self.login_button.setObjectName("loginButton")
        self.login_button.clicked.connect(self.login)
        buttons_layout.addWidget(self.login_button)
        
        # زر الإلغاء
        cancel_button = QPushButton("إلغاء")
        cancel_button.setObjectName("cancelButton")
        cancel_button.clicked.connect(self.close)
        buttons_layout.addWidget(cancel_button)
        
        layout.addLayout(buttons_layout)
    
    def create_footer(self, layout):
        """إنشاء تذييل النافذة"""
        # معلومات الإصدار
        version_label = QLabel(f"الإصدار {settings.app_version}")
        version_label.setObjectName("versionLabel")
        version_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(version_label)
        
        # حالة الاتصال
        self.status_label = QLabel("جاهز للاتصال")
        self.status_label.setObjectName("statusLabel")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.status_label)
    
    def apply_styles(self):
        """تطبيق الأنماط"""
        style = """
        QWidget#loginFrame {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #667eea, stop:1 #764ba2);
            border-radius: 15px;
        }
        
        QLabel#titleLabel {
            font-size: 24px;
            font-weight: bold;
            color: white;
            margin: 10px 0;
        }
        
        QLabel#subtitleLabel {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 20px;
        }
        
        QLabel#fieldLabel {
            font-size: 14px;
            font-weight: bold;
            color: white;
            margin-bottom: 5px;
        }
        
        QLineEdit#inputField {
            padding: 12px;
            font-size: 14px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
        }
        
        QLineEdit#inputField:focus {
            border: 2px solid #4CAF50;
            background: white;
        }
        
        QPushButton#loginButton {
            padding: 12px 24px;
            font-size: 14px;
            font-weight: bold;
            color: white;
            background: #4CAF50;
            border: none;
            border-radius: 8px;
        }
        
        QPushButton#loginButton:hover {
            background: #45a049;
        }
        
        QPushButton#loginButton:pressed {
            background: #3d8b40;
        }
        
        QPushButton#loginButton:disabled {
            background: #cccccc;
            color: #666666;
        }
        
        QPushButton#cancelButton {
            padding: 12px 24px;
            font-size: 14px;
            color: white;
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
        }
        
        QPushButton#cancelButton:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        QLabel#versionLabel {
            font-size: 10px;
            color: rgba(255, 255, 255, 0.6);
            margin-top: 20px;
        }
        
        QLabel#statusLabel {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 5px;
        }
        
        QFrame#separatorLine {
            color: rgba(255, 255, 255, 0.3);
        }
        """
        self.setStyleSheet(style)
    
    def login(self):
        """تسجيل الدخول"""
        username = self.username_input.text().strip()
        password = self.password_input.text()
        
        if not username or not password:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        # تعطيل الأزرار أثناء المعالجة
        self.login_button.setEnabled(False)
        self.login_button.setText("جاري تسجيل الدخول...")
        self.status_label.setText("جاري الاتصال بالخادم...")
        
        # بدء خيط تسجيل الدخول
        self.login_thread = LoginThread(username, password)
        self.login_thread.login_success.connect(self.on_login_success)
        self.login_thread.login_failed.connect(self.on_login_failed)
        self.login_thread.start()
    
    @pyqtSlot(dict)
    def on_login_success(self, result):
        """نجح تسجيل الدخول"""
        self.status_label.setText("تم تسجيل الدخول بنجاح")
        self.login_successful.emit(result)
        self.close()
    
    @pyqtSlot(str)
    def on_login_failed(self, error_message):
        """فشل تسجيل الدخول"""
        self.login_button.setEnabled(True)
        self.login_button.setText("تسجيل الدخول")
        self.status_label.setText("فشل تسجيل الدخول")
        
        QMessageBox.critical(self, "خطأ في تسجيل الدخول", error_message)
        
        # تركيز على حقل كلمة المرور
        self.password_input.clear()
        self.password_input.setFocus()
    
    def keyPressEvent(self, event):
        """معالجة ضغط المفاتيح"""
        if event.key() == Qt.Key.Key_Escape:
            self.login_cancelled.emit()
            self.close()
        super().keyPressEvent(event)

    def closeEvent(self, event):
        """معالجة إغلاق النافذة"""
        self.login_cancelled.emit()
        super().closeEvent(event)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
    
    window = LoginWindow()
    window.show()
    
    sys.exit(app.exec())
