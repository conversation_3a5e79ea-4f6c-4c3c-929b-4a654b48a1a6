# تطبيق المحاسبة المتكامل

تطبيق محاسبة شامل مصمم خصيصاً لشركات الإنترنت وإدارة المشتركين مع دعم العمل المتزامن عبر الشبكة.

## المميزات الرئيسية

### إدارة المشتركين
- إضافة وإدارة المشتركين
- تسليم الراوترات وتتبعها
- تجديد الباقات والاشتراكات
- استعلامات مفصلة حسب التاريخ

### النظام المحاسبي
- إدارة المبيعات والمشتريات
- تتبع المصاريف والرواتب
- إدارة الموردين والعملاء
- سندات القبض والدفع
- إغلاق الصندوق اليومي

### إدارة الجرد
- تصنيف المنتجات والوحدات
- تتبع المخزون
- إدارة الكبلات للعمال
- تقارير الجرد الأسبوعية

### نظام الصلاحيات
- مدير: صلاحيات كاملة
- مستخدم: صلاحيات محدودة
- كاشير: نقطة البيع

### المزامنة والنسخ الاحتياطي
- مزامنة تلقائية مع Google Drive
- نسخ احتياطية عند إغلاق الصندوق
- عمل متزامن عبر الشبكة

### نظام الطباعة
- دعم الطابعات الحرارية 80mm
- دعم طابعات A4
- حفظ كـ PDF عند عدم وجود طابعة
- قوالب طباعة قابلة للتخصيص

## 🚀 التثبيت والتشغيل

### المتطلبات
- Python 3.8 أو أحدث
- نظام التشغيل: Windows, macOS, أو Linux
- اتصال بالإنترنت للمزامنة (اختياري)

### التثبيت السريع

1. **استنساخ المشروع:**
```bash
git clone <repository-url>
cd الشامل
```

2. **إنشاء بيئة افتراضية:**
```bash
python -m venv venv

# Windows
venv\Scripts\activate

# Linux/macOS
source venv/bin/activate
```

3. **تثبيت المتطلبات:**
```bash
pip install -r requirements.txt
```

4. **إعداد الإعدادات:**
```bash
# نسخ ملف الإعدادات
cp config/.env.example config/.env

# تعديل الإعدادات حسب الحاجة
notepad config/.env  # Windows
nano config/.env     # Linux/macOS
```

5. **تشغيل التطبيق:**
```bash
python run.py
```

أو مباشرة:
```bash
python main.py
```

### التشغيل السريع
```bash
# تشغيل مع فحص المتطلبات تلقائياً
python run.py
```

## التثبيت

1. استنساخ المشروع:
```bash
git clone [repository-url]
cd accounting-app
```

2. إنشاء بيئة افتراضية:
```bash
python -m venv venv
source venv/bin/activate  # Linux/macOS
# أو
venv\Scripts\activate  # Windows
```

3. تثبيت المتطلبات:
```bash
pip install -r requirements.txt
```

4. إعداد قاعدة البيانات:
```bash
python -m alembic upgrade head
```

5. تشغيل التطبيق:
```bash
python main.py
```

## الهيكل العام

```
accounting-app/
├── app/
│   ├── api/              # FastAPI endpoints
│   ├── core/             # إعدادات أساسية
│   ├── db/               # قاعدة البيانات
│   ├── models/           # نماذج البيانات
│   ├── services/         # خدمات العمل
│   └── utils/            # أدوات مساعدة
├── gui/
│   ├── windows/          # نوافذ التطبيق
│   ├── widgets/          # عناصر واجهة مخصصة
│   └── resources/        # الموارد والأيقونات
├── printing/
│   ├── templates/        # قوالب الطباعة
│   └── drivers/          # تعريفات الطابعات
├── sync/
│   └── google_drive/     # مزامنة Google Drive
├── tests/                # الاختبارات
├── alembic/              # ملفات قاعدة البيانات
├── config/               # ملفات الإعداد
└── data/                 # البيانات المحلية
```

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.
