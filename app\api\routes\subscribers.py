"""
مسارات إدارة المشتركين
"""
from typing import List, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import or_, and_
from pydantic import BaseModel, EmailStr

from app.db.database import get_db
from app.services.auth import get_current_active_user, require_permission
from app.models.user import User
from app.models.subscriber import Subscriber
from app.models.package import Package
from app.models.router import Router

router = APIRouter()


class SubscriberCreate(BaseModel):
    """نموذج إنشاء مشترك"""
    name: str
    phone: str
    email: Optional[EmailStr] = None
    address: str
    national_id: Optional[str] = None
    package_id: Optional[int] = None
    notes: Optional[str] = None


class SubscriberUpdate(BaseModel):
    """نموذج تحديث مشترك"""
    name: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[EmailStr] = None
    address: Optional[str] = None
    national_id: Optional[str] = None
    package_id: Optional[int] = None
    is_active: Optional[bool] = None
    notes: Optional[str] = None


class SubscriberResponse(BaseModel):
    """استجابة المشترك"""
    id: int
    name: str
    phone: str
    email: Optional[str]
    address: str
    national_id: Optional[str]
    package_id: Optional[int]
    router_id: Optional[int]
    subscription_start: Optional[datetime]
    subscription_end: Optional[datetime]
    is_active: bool
    balance: float
    total_paid: float
    notes: Optional[str]
    created_at: datetime
    
    # معلومات إضافية
    package_name: Optional[str] = None
    router_serial: Optional[str] = None
    days_remaining: Optional[int] = None

    class Config:
        from_attributes = True


class RenewalRequest(BaseModel):
    """طلب تجديد الاشتراك"""
    package_id: int
    duration_days: int = 30
    payment_amount: float
    payment_method: str = "cash"
    notes: Optional[str] = None


@router.get("/", response_model=List[SubscriberResponse])
async def get_subscribers(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = Query(None, description="البحث في الاسم أو الهاتف"),
    package_id: Optional[int] = Query(None, description="فلترة حسب الباقة"),
    is_active: Optional[bool] = Query(None, description="فلترة حسب الحالة"),
    current_user: User = Depends(require_permission("subscribers")),
    db: Session = Depends(get_db)
):
    """الحصول على قائمة المشتركين"""
    query = db.query(Subscriber)
    
    # البحث
    if search:
        query = query.filter(
            or_(
                Subscriber.name.contains(search),
                Subscriber.phone.contains(search)
            )
        )
    
    # فلترة حسب الباقة
    if package_id:
        query = query.filter(Subscriber.package_id == package_id)
    
    # فلترة حسب الحالة
    if is_active is not None:
        query = query.filter(Subscriber.is_active == is_active)
    
    subscribers = query.offset(skip).limit(limit).all()
    
    # إضافة معلومات إضافية
    result = []
    for subscriber in subscribers:
        subscriber_data = SubscriberResponse.from_orm(subscriber)
        
        # اسم الباقة
        if subscriber.package:
            subscriber_data.package_name = subscriber.package.name
        
        # رقم الراوتر
        if subscriber.router:
            subscriber_data.router_serial = subscriber.router.serial_number
        
        # الأيام المتبقية
        if subscriber.subscription_end:
            days_remaining = (subscriber.subscription_end - datetime.now()).days
            subscriber_data.days_remaining = max(0, days_remaining)
        
        result.append(subscriber_data)
    
    return result


@router.get("/{subscriber_id}", response_model=SubscriberResponse)
async def get_subscriber(
    subscriber_id: int,
    current_user: User = Depends(require_permission("subscribers")),
    db: Session = Depends(get_db)
):
    """الحصول على مشترك محدد"""
    subscriber = db.query(Subscriber).filter(Subscriber.id == subscriber_id).first()
    if not subscriber:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="المشترك غير موجود"
        )
    
    subscriber_data = SubscriberResponse.from_orm(subscriber)
    
    # إضافة معلومات إضافية
    if subscriber.package:
        subscriber_data.package_name = subscriber.package.name
    
    if subscriber.router:
        subscriber_data.router_serial = subscriber.router.serial_number
    
    if subscriber.subscription_end:
        days_remaining = (subscriber.subscription_end - datetime.now()).days
        subscriber_data.days_remaining = max(0, days_remaining)
    
    return subscriber_data


@router.post("/", response_model=SubscriberResponse)
async def create_subscriber(
    subscriber_data: SubscriberCreate,
    current_user: User = Depends(require_permission("subscribers")),
    db: Session = Depends(get_db)
):
    """إنشاء مشترك جديد"""
    # التحقق من عدم وجود رقم الهاتف
    existing_phone = db.query(Subscriber).filter(Subscriber.phone == subscriber_data.phone).first()
    if existing_phone:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="رقم الهاتف موجود بالفعل"
        )
    
    # التحقق من الرقم القومي إذا تم توفيره
    if subscriber_data.national_id:
        existing_national_id = db.query(Subscriber).filter(
            Subscriber.national_id == subscriber_data.national_id
        ).first()
        if existing_national_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="الرقم القومي موجود بالفعل"
            )
    
    # إنشاء المشترك
    subscriber = Subscriber(
        name=subscriber_data.name,
        phone=subscriber_data.phone,
        email=subscriber_data.email,
        address=subscriber_data.address,
        national_id=subscriber_data.national_id,
        package_id=subscriber_data.package_id,
        notes=subscriber_data.notes,
        created_by=current_user.id
    )
    
    db.add(subscriber)
    db.commit()
    db.refresh(subscriber)
    
    return SubscriberResponse.from_orm(subscriber)


@router.put("/{subscriber_id}", response_model=SubscriberResponse)
async def update_subscriber(
    subscriber_id: int,
    subscriber_data: SubscriberUpdate,
    current_user: User = Depends(require_permission("subscribers")),
    db: Session = Depends(get_db)
):
    """تحديث مشترك"""
    subscriber = db.query(Subscriber).filter(Subscriber.id == subscriber_id).first()
    if not subscriber:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="المشترك غير موجود"
        )
    
    # تحديث البيانات
    update_data = subscriber_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(subscriber, field, value)
    
    db.commit()
    db.refresh(subscriber)
    
    return SubscriberResponse.from_orm(subscriber)


@router.post("/{subscriber_id}/renew")
async def renew_subscription(
    subscriber_id: int,
    renewal_data: RenewalRequest,
    current_user: User = Depends(require_permission("subscribers")),
    db: Session = Depends(get_db)
):
    """تجديد اشتراك المشترك"""
    subscriber = db.query(Subscriber).filter(Subscriber.id == subscriber_id).first()
    if not subscriber:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="المشترك غير موجود"
        )
    
    # التحقق من الباقة
    package = db.query(Package).filter(Package.id == renewal_data.package_id).first()
    if not package:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الباقة غير موجودة"
        )
    
    # تحديث الاشتراك
    if subscriber.subscription_end and subscriber.subscription_end > datetime.now():
        # إضافة المدة للاشتراك الحالي
        subscriber.subscription_end += timedelta(days=renewal_data.duration_days)
    else:
        # بدء اشتراك جديد
        subscriber.subscription_start = datetime.now()
        subscriber.subscription_end = datetime.now() + timedelta(days=renewal_data.duration_days)
    
    subscriber.package_id = renewal_data.package_id
    subscriber.is_active = True
    subscriber.add_payment(renewal_data.payment_amount)
    
    # إنشاء معاملة التجديد
    from app.models.transaction import Transaction
    transaction = Transaction(
        transaction_number=f"REN-{datetime.now().strftime('%Y%m%d%H%M%S')}",
        type="renewal",
        amount=renewal_data.payment_amount,
        payment_method=renewal_data.payment_method,
        description=f"تجديد اشتراك {renewal_data.duration_days} يوم",
        notes=renewal_data.notes,
        subscriber_id=subscriber_id,
        package_id=renewal_data.package_id,
        user_id=current_user.id
    )
    
    db.add(transaction)
    db.commit()
    db.refresh(subscriber)
    
    return {
        "message": "تم تجديد الاشتراك بنجاح",
        "subscription_end": subscriber.subscription_end,
        "transaction_number": transaction.transaction_number
    }
