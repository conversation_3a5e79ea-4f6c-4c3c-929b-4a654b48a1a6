"""
مسارات إدارة الكبلات والعمال
"""
from typing import List, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import func
from pydantic import BaseModel

from app.db.database import get_db
from app.services.auth import get_current_active_user, require_permission
from app.models.user import User
from app.models.cable import Employee, CableType, CableAssignment, CableUsage, WeeklyCableReport

router = APIRouter()


class EmployeeCreate(BaseModel):
    name: str
    phone: Optional[str] = None
    email: Optional[str] = None
    address: Optional[str] = None
    employee_id: Optional[str] = None
    position: Optional[str] = None
    department: Optional[str] = None
    hire_date: Optional[datetime] = None
    cable_limit: int = 100
    notes: Optional[str] = None


class CableTypeCreate(BaseModel):
    name: str
    description: Optional[str] = None
    length: Optional[int] = None
    color: Optional[str] = None
    category: Optional[str] = None
    cost_price: float = 0.0
    selling_price: float = 0.0
    current_stock: int = 0
    min_stock: int = 10


class CableAssignmentCreate(BaseModel):
    employee_id: int
    cable_type_id: int
    quantity: int
    notes: Optional[str] = None
    reference_number: Optional[str] = None


class CableUsageCreate(BaseModel):
    employee_id: int
    cable_type_id: int
    quantity: int
    subscriber_id: Optional[int] = None
    router_id: Optional[int] = None
    usage_type: str = "installation"  # installation, repair, replacement
    notes: Optional[str] = None
    location: Optional[str] = None


@router.get("/employees/")
async def get_employees(
    skip: int = 0,
    limit: int = 100,
    is_active: Optional[bool] = Query(None, description="فلترة حسب الحالة"),
    current_user: User = Depends(require_permission("cables")),
    db: Session = Depends(get_db)
):
    """الحصول على قائمة العمال"""
    query = db.query(Employee)
    
    if is_active is not None:
        query = query.filter(Employee.is_active == is_active)
    
    employees = query.offset(skip).limit(limit).all()
    
    result = []
    for employee in employees:
        result.append({
            "id": employee.id,
            "name": employee.name,
            "phone": employee.phone,
            "employee_id": employee.employee_id,
            "position": employee.position,
            "department": employee.department,
            "cable_balance": employee.cable_balance,
            "cable_limit": employee.cable_limit,
            "can_receive_cables": employee.can_receive_cables,
            "cables_used_this_week": employee.cables_used_this_week,
            "is_active": employee.is_active,
            "created_at": employee.created_at
        })
    
    return result


@router.post("/employees/")
async def create_employee(
    employee_data: EmployeeCreate,
    current_user: User = Depends(require_permission("cables")),
    db: Session = Depends(get_db)
):
    """إنشاء عامل جديد"""
    # التحقق من رقم الموظف إذا تم توفيره
    if employee_data.employee_id:
        existing_employee = db.query(Employee).filter(
            Employee.employee_id == employee_data.employee_id
        ).first()
        if existing_employee:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="رقم الموظف موجود بالفعل"
            )
    
    employee = Employee(**employee_data.dict())
    db.add(employee)
    db.commit()
    db.refresh(employee)
    
    return employee


@router.get("/cable-types/")
async def get_cable_types(
    current_user: User = Depends(require_permission("cables")),
    db: Session = Depends(get_db)
):
    """الحصول على أنواع الكبلات"""
    cable_types = db.query(CableType).filter(CableType.is_active == True).all()
    return cable_types


@router.post("/cable-types/")
async def create_cable_type(
    cable_data: CableTypeCreate,
    current_user: User = Depends(require_permission("cables")),
    db: Session = Depends(get_db)
):
    """إنشاء نوع كبل جديد"""
    # التحقق من عدم وجود الاسم
    existing_cable = db.query(CableType).filter(CableType.name == cable_data.name).first()
    if existing_cable:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="اسم نوع الكبل موجود بالفعل"
        )
    
    cable_type = CableType(**cable_data.dict())
    db.add(cable_type)
    db.commit()
    db.refresh(cable_type)
    
    return cable_type


@router.post("/assignments/")
async def assign_cables(
    assignment_data: CableAssignmentCreate,
    current_user: User = Depends(require_permission("cables")),
    db: Session = Depends(get_db)
):
    """تسليم كبلات للعامل"""
    # التحقق من العامل
    employee = db.query(Employee).filter(Employee.id == assignment_data.employee_id).first()
    if not employee:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="العامل غير موجود"
        )
    
    if not employee.is_active:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="العامل غير نشط"
        )
    
    # التحقق من نوع الكبل
    cable_type = db.query(CableType).filter(CableType.id == assignment_data.cable_type_id).first()
    if not cable_type:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="نوع الكبل غير موجود"
        )
    
    # التحقق من المخزون
    if cable_type.current_stock < assignment_data.quantity:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="المخزون غير كافي"
        )
    
    # التحقق من حد العامل
    if employee.cable_balance + assignment_data.quantity > employee.cable_limit:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="تجاوز الحد الأقصى للكبلات للعامل"
        )
    
    # تسليم الكبلات
    assignment = CableAssignment(
        employee_id=assignment_data.employee_id,
        cable_type_id=assignment_data.cable_type_id,
        quantity=assignment_data.quantity,
        assigned_by=current_user.id,
        notes=assignment_data.notes,
        reference_number=assignment_data.reference_number
    )
    
    # تحديث رصيد العامل والمخزون
    employee.cable_balance += assignment_data.quantity
    cable_type.current_stock -= assignment_data.quantity
    
    db.add(assignment)
    db.commit()
    db.refresh(assignment)
    
    return {
        "message": "تم تسليم الكبلات بنجاح",
        "assignment_id": assignment.id,
        "employee_name": employee.name,
        "cable_type": cable_type.name,
        "quantity": assignment_data.quantity,
        "employee_new_balance": employee.cable_balance
    }


@router.post("/usages/")
async def record_cable_usage(
    usage_data: CableUsageCreate,
    current_user: User = Depends(require_permission("cables")),
    db: Session = Depends(get_db)
):
    """تسجيل استخدام كبلات"""
    # التحقق من العامل
    employee = db.query(Employee).filter(Employee.id == usage_data.employee_id).first()
    if not employee:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="العامل غير موجود"
        )
    
    # التحقق من رصيد العامل
    if employee.cable_balance < usage_data.quantity:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="رصيد العامل غير كافي"
        )
    
    # تسجيل الاستخدام
    usage = CableUsage(
        employee_id=usage_data.employee_id,
        cable_type_id=usage_data.cable_type_id,
        quantity=usage_data.quantity,
        subscriber_id=usage_data.subscriber_id,
        router_id=usage_data.router_id,
        usage_type=usage_data.usage_type,
        notes=usage_data.notes,
        location=usage_data.location
    )
    
    # خصم من رصيد العامل
    employee.cable_balance -= usage_data.quantity
    
    db.add(usage)
    db.commit()
    db.refresh(usage)
    
    return {
        "message": "تم تسجيل استخدام الكبلات بنجاح",
        "usage_id": usage.id,
        "employee_name": employee.name,
        "quantity_used": usage_data.quantity,
        "employee_remaining_balance": employee.cable_balance
    }


@router.get("/assignments/")
async def get_cable_assignments(
    skip: int = 0,
    limit: int = 100,
    employee_id: Optional[int] = Query(None, description="فلترة حسب العامل"),
    start_date: Optional[datetime] = Query(None, description="تاريخ البداية"),
    end_date: Optional[datetime] = Query(None, description="تاريخ النهاية"),
    current_user: User = Depends(require_permission("cables")),
    db: Session = Depends(get_db)
):
    """الحصول على تسليمات الكبلات"""
    query = db.query(CableAssignment)
    
    if employee_id:
        query = query.filter(CableAssignment.employee_id == employee_id)
    
    if start_date:
        query = query.filter(CableAssignment.assignment_date >= start_date)
    
    if end_date:
        query = query.filter(CableAssignment.assignment_date <= end_date)
    
    assignments = query.order_by(CableAssignment.assignment_date.desc()).offset(skip).limit(limit).all()
    
    result = []
    for assignment in assignments:
        result.append({
            "id": assignment.id,
            "employee_name": assignment.employee.name,
            "cable_type_name": assignment.cable_type.name,
            "quantity": assignment.quantity,
            "assignment_date": assignment.assignment_date,
            "assigned_by": assignment.assigner.full_name,
            "notes": assignment.notes,
            "reference_number": assignment.reference_number
        })
    
    return result


@router.get("/usages/")
async def get_cable_usages(
    skip: int = 0,
    limit: int = 100,
    employee_id: Optional[int] = Query(None, description="فلترة حسب العامل"),
    start_date: Optional[datetime] = Query(None, description="تاريخ البداية"),
    end_date: Optional[datetime] = Query(None, description="تاريخ النهاية"),
    current_user: User = Depends(require_permission("cables")),
    db: Session = Depends(get_db)
):
    """الحصول على استخدامات الكبلات"""
    query = db.query(CableUsage)
    
    if employee_id:
        query = query.filter(CableUsage.employee_id == employee_id)
    
    if start_date:
        query = query.filter(CableUsage.usage_date >= start_date)
    
    if end_date:
        query = query.filter(CableUsage.usage_date <= end_date)
    
    usages = query.order_by(CableUsage.usage_date.desc()).offset(skip).limit(limit).all()
    
    result = []
    for usage in usages:
        result.append({
            "id": usage.id,
            "employee_name": usage.employee.name,
            "cable_type_name": usage.cable_type.name,
            "quantity": usage.quantity,
            "usage_type": usage.usage_type,
            "usage_date": usage.usage_date,
            "subscriber_name": usage.subscriber.name if usage.subscriber else None,
            "router_serial": usage.router.serial_number if usage.router else None,
            "location": usage.location,
            "notes": usage.notes
        })
    
    return result


@router.post("/weekly-reports/generate")
async def generate_weekly_cable_report(
    employee_id: int,
    week_start: datetime,
    current_user: User = Depends(require_permission("cables")),
    db: Session = Depends(get_db)
):
    """إنشاء تقرير كبلات أسبوعي"""
    employee = db.query(Employee).filter(Employee.id == employee_id).first()
    if not employee:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="العامل غير موجود"
        )
    
    week_end = week_start + timedelta(days=7)
    
    # حساب الإحصائيات
    assignments = db.query(CableAssignment).filter(
        CableAssignment.employee_id == employee_id,
        CableAssignment.assignment_date >= week_start,
        CableAssignment.assignment_date < week_end
    ).all()
    
    usages = db.query(CableUsage).filter(
        CableUsage.employee_id == employee_id,
        CableUsage.usage_date >= week_start,
        CableUsage.usage_date < week_end
    ).all()
    
    cables_assigned = sum(a.quantity for a in assignments)
    cables_used = sum(u.quantity for u in usages)
    cables_remaining = employee.cable_balance
    
    # إنشاء التقرير
    report = WeeklyCableReport(
        employee_id=employee_id,
        week_start=week_start,
        week_end=week_end,
        cables_assigned=cables_assigned,
        cables_used=cables_used,
        cables_remaining=cables_remaining,
        generated_by=current_user.id
    )
    
    db.add(report)
    db.commit()
    db.refresh(report)
    
    return {
        "message": "تم إنشاء التقرير الأسبوعي بنجاح",
        "report_id": report.id,
        "employee_name": employee.name,
        "week_start": week_start,
        "week_end": week_end,
        "cables_assigned": cables_assigned,
        "cables_used": cables_used,
        "cables_remaining": cables_remaining
    }
