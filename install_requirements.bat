@echo off
chcp 65001 >nul
title تثبيت متطلبات التطبيق

echo ================================================================
echo 📦 تثبيت متطلبات تطبيق المحاسبة المتكامل
echo ================================================================
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت!
    echo 📥 يرجى تحميل Python من: https://python.org/downloads
    pause
    exit /b 1
)

echo ✅ Python متوفر
python --version

REM إنشاء البيئة الافتراضية إذا لم تكن موجودة
if not exist "venv" (
    echo 🔧 إنشاء البيئة الافتراضية...
    python -m venv venv
    if errorlevel 1 (
        echo ❌ فشل في إنشاء البيئة الافتراضية
        pause
        exit /b 1
    )
    echo ✅ تم إنشاء البيئة الافتراضية
)

echo.
echo 📦 تثبيت المتطلبات الأساسية...
venv\Scripts\pip install --upgrade pip

echo.
echo 📦 تثبيت مكتبات التطبيق...
venv\Scripts\pip install fastapi uvicorn sqlalchemy pydantic

echo.
echo 📦 تثبيت مكتبات المصادقة...
venv\Scripts\pip install python-jose[cryptography] passlib[bcrypt] python-multipart

echo.
echo 📦 تثبيت مكتبات الإعدادات...
venv\Scripts\pip install pydantic-settings python-dotenv email-validator

echo.
echo 📦 تثبيت مكتبات الواجهة...
venv\Scripts\pip install PyQt6

echo.
echo 📦 تثبيت مكتبات إضافية...
venv\Scripts\pip install schedule requests reportlab

echo.
echo 📦 تثبيت مكتبات Google Drive (اختياري)...
venv\Scripts\pip install google-auth google-auth-oauthlib google-auth-httplib2 google-api-python-client

if errorlevel 1 (
    echo ⚠️ تحذير: بعض المكتبات لم يتم تثبيتها
    echo 💡 التطبيق قد يعمل بوظائف محدودة
) else (
    echo ✅ تم تثبيت جميع المتطلبات بنجاح!
)

echo.
echo ================================================================
echo 🎉 Installation completed
echo ================================================================
echo 💡 يمكنك الآن تشغيل التطبيق باستخدام:
echo    start_venv.bat
echo.
pause
