"""
مسارات التقارير والإحصائيات
"""
from typing import List, Optional
from datetime import datetime, date, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_
from pydantic import BaseModel
import json

from app.db.database import get_db
from app.services.auth import get_current_active_user, require_permission
from app.models.user import User
from app.models.report import Report, DailyReport, MonthlyReport
from app.models.subscriber import Subscriber
from app.models.transaction import Transaction
from app.models.router import Router

router = APIRouter()


class ReportCreate(BaseModel):
    name: str
    type: str  # daily, weekly, monthly, yearly, custom
    category: str  # financial, subscribers, inventory, cables
    start_date: datetime
    end_date: datetime
    parameters: Optional[dict] = None
    filters: Optional[dict] = None
    description: Optional[str] = None


@router.get("/daily/{report_date}")
async def get_daily_report(
    report_date: date,
    current_user: User = Depends(require_permission("reports")),
    db: Session = Depends(get_db)
):
    """الحصول على التقرير اليومي"""
    # البحث عن تقرير موجود
    existing_report = db.query(DailyReport).filter(
        func.date(DailyReport.date) == report_date
    ).first()
    
    if existing_report:
        return {
            "id": existing_report.id,
            "date": existing_report.date,
            "total_sales": existing_report.total_sales,
            "total_subscriptions": existing_report.total_subscriptions,
            "total_renewals": existing_report.total_renewals,
            "total_router_sales": existing_report.total_router_sales,
            "new_subscribers": existing_report.new_subscribers,
            "active_subscribers": existing_report.active_subscribers,
            "routers_delivered": existing_report.routers_delivered,
            "routers_returned": existing_report.routers_returned,
            "cash_income": existing_report.cash_income,
            "card_income": existing_report.card_income,
            "total_expenses": existing_report.total_expenses,
            "net_income": existing_report.net_income,
            "opening_balance": existing_report.opening_balance,
            "closing_balance": existing_report.closing_balance
        }
    
    # إنشاء تقرير جديد
    start_datetime = datetime.combine(report_date, datetime.min.time())
    end_datetime = datetime.combine(report_date, datetime.max.time())
    
    # المعاملات اليومية
    transactions = db.query(Transaction).filter(
        and_(
            Transaction.transaction_date >= start_datetime,
            Transaction.transaction_date <= end_datetime
        )
    ).all()
    
    # حساب الإحصائيات
    total_sales = sum(t.amount for t in transactions if t.is_income)
    total_expenses = sum(t.amount for t in transactions if t.is_expense)
    
    subscriptions = [t for t in transactions if t.type == "subscription"]
    renewals = [t for t in transactions if t.type == "renewal"]
    router_sales = [t for t in transactions if t.type == "router_sale"]
    
    cash_income = sum(t.amount for t in transactions if t.payment_method == "cash" and t.is_income)
    card_income = sum(t.amount for t in transactions if t.payment_method == "card" and t.is_income)
    
    # المشتركين الجدد
    new_subscribers = db.query(Subscriber).filter(
        func.date(Subscriber.created_at) == report_date
    ).count()
    
    # المشتركين النشطين
    active_subscribers = db.query(Subscriber).filter(
        Subscriber.is_active == True
    ).count()
    
    # الراوترات المسلمة والمرجعة
    routers_delivered = db.query(Router).filter(
        func.date(Router.assigned_date) == report_date
    ).count()
    
    routers_returned = db.query(Router).filter(
        func.date(Router.returned_date) == report_date
    ).count()
    
    # إنشاء التقرير
    daily_report = DailyReport(
        date=start_datetime,
        user_id=current_user.id,
        total_sales=total_sales,
        total_subscriptions=len(subscriptions),
        total_renewals=len(renewals),
        total_router_sales=len(router_sales),
        new_subscribers=new_subscribers,
        active_subscribers=active_subscribers,
        routers_delivered=routers_delivered,
        routers_returned=routers_returned,
        cash_income=cash_income,
        card_income=card_income,
        total_expenses=total_expenses,
        net_income=total_sales - total_expenses
    )
    
    db.add(daily_report)
    db.commit()
    db.refresh(daily_report)
    
    return {
        "id": daily_report.id,
        "date": daily_report.date,
        "total_sales": daily_report.total_sales,
        "total_subscriptions": daily_report.total_subscriptions,
        "total_renewals": daily_report.total_renewals,
        "total_router_sales": daily_report.total_router_sales,
        "new_subscribers": daily_report.new_subscribers,
        "active_subscribers": daily_report.active_subscribers,
        "routers_delivered": daily_report.routers_delivered,
        "routers_returned": daily_report.routers_returned,
        "cash_income": daily_report.cash_income,
        "card_income": daily_report.card_income,
        "total_expenses": daily_report.total_expenses,
        "net_income": daily_report.net_income
    }


@router.get("/monthly/{year}/{month}")
async def get_monthly_report(
    year: int,
    month: int,
    current_user: User = Depends(require_permission("reports")),
    db: Session = Depends(get_db)
):
    """الحصول على التقرير الشهري"""
    # البحث عن تقرير موجود
    existing_report = db.query(MonthlyReport).filter(
        and_(MonthlyReport.year == year, MonthlyReport.month == month)
    ).first()
    
    if existing_report:
        return existing_report
    
    # إنشاء تقرير جديد
    start_date = datetime(year, month, 1)
    if month == 12:
        end_date = datetime(year + 1, 1, 1) - timedelta(days=1)
    else:
        end_date = datetime(year, month + 1, 1) - timedelta(days=1)
    
    # المعاملات الشهرية
    transactions = db.query(Transaction).filter(
        and_(
            Transaction.transaction_date >= start_date,
            Transaction.transaction_date <= end_date
        )
    ).all()
    
    # حساب الإحصائيات
    total_revenue = sum(t.amount for t in transactions if t.is_income)
    total_expenses = sum(t.amount for t in transactions if t.is_expense)
    
    subscription_revenue = sum(t.amount for t in transactions if t.type in ["subscription", "renewal"])
    router_sales_revenue = sum(t.amount for t in transactions if t.type == "router_sale")
    
    # المشتركين الجدد
    new_subscribers_count = db.query(Subscriber).filter(
        and_(
            Subscriber.created_at >= start_date,
            Subscriber.created_at <= end_date
        )
    ).count()
    
    total_subscribers = db.query(Subscriber).filter(
        Subscriber.is_active == True
    ).count()
    
    # إنشاء التقرير
    monthly_report = MonthlyReport(
        year=year,
        month=month,
        total_revenue=total_revenue,
        total_expenses=total_expenses,
        net_profit=total_revenue - total_expenses,
        new_subscribers_count=new_subscribers_count,
        total_subscribers=total_subscribers,
        subscription_revenue=subscription_revenue,
        router_sales_revenue=router_sales_revenue,
        generated_by=current_user.id
    )
    
    db.add(monthly_report)
    db.commit()
    db.refresh(monthly_report)
    
    return monthly_report


@router.get("/financial-summary")
async def get_financial_summary(
    start_date: date = Query(..., description="تاريخ البداية"),
    end_date: date = Query(..., description="تاريخ النهاية"),
    current_user: User = Depends(require_permission("reports")),
    db: Session = Depends(get_db)
):
    """ملخص مالي لفترة محددة"""
    start_datetime = datetime.combine(start_date, datetime.min.time())
    end_datetime = datetime.combine(end_date, datetime.max.time())
    
    # المعاملات في الفترة
    transactions = db.query(Transaction).filter(
        and_(
            Transaction.transaction_date >= start_datetime,
            Transaction.transaction_date <= end_datetime
        )
    ).all()
    
    # تصنيف المعاملات
    income_transactions = [t for t in transactions if t.is_income]
    expense_transactions = [t for t in transactions if t.is_expense]
    
    # إجماليات
    total_income = sum(t.amount for t in income_transactions)
    total_expenses = sum(t.amount for t in expense_transactions)
    net_profit = total_income - total_expenses
    
    # تصنيف الدخل
    income_by_type = {}
    for transaction in income_transactions:
        income_by_type[transaction.type] = income_by_type.get(transaction.type, 0) + transaction.amount
    
    # تصنيف المصروفات
    expenses_by_type = {}
    for transaction in expense_transactions:
        expenses_by_type[transaction.type] = expenses_by_type.get(transaction.type, 0) + transaction.amount
    
    # طرق الدفع
    payment_methods = {}
    for transaction in income_transactions:
        payment_methods[transaction.payment_method] = payment_methods.get(transaction.payment_method, 0) + transaction.amount
    
    return {
        "period": {
            "start_date": start_date,
            "end_date": end_date,
            "days": (end_date - start_date).days + 1
        },
        "summary": {
            "total_income": total_income,
            "total_expenses": total_expenses,
            "net_profit": net_profit,
            "profit_margin": (net_profit / total_income * 100) if total_income > 0 else 0,
            "transactions_count": len(transactions)
        },
        "income_breakdown": income_by_type,
        "expenses_breakdown": expenses_by_type,
        "payment_methods": payment_methods
    }


@router.get("/subscribers-analysis")
async def get_subscribers_analysis(
    current_user: User = Depends(require_permission("reports")),
    db: Session = Depends(get_db)
):
    """تحليل المشتركين"""
    # إجمالي المشتركين
    total_subscribers = db.query(Subscriber).count()
    active_subscribers = db.query(Subscriber).filter(Subscriber.is_active == True).count()
    inactive_subscribers = total_subscribers - active_subscribers
    
    # المشتركين الجدد هذا الشهر
    current_month_start = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    new_this_month = db.query(Subscriber).filter(
        Subscriber.created_at >= current_month_start
    ).count()
    
    # المشتركين حسب الباقات
    subscribers_by_package = db.query(
        Subscriber.package_id,
        func.count(Subscriber.id).label('count')
    ).filter(
        Subscriber.is_active == True
    ).group_by(Subscriber.package_id).all()
    
    # الاشتراكات المنتهية الصلاحية
    expired_subscriptions = db.query(Subscriber).filter(
        and_(
            Subscriber.subscription_end < datetime.now(),
            Subscriber.is_active == True
        )
    ).count()
    
    # الاشتراكات التي ستنتهي خلال أسبوع
    week_from_now = datetime.now() + timedelta(days=7)
    expiring_soon = db.query(Subscriber).filter(
        and_(
            Subscriber.subscription_end <= week_from_now,
            Subscriber.subscription_end > datetime.now(),
            Subscriber.is_active == True
        )
    ).count()
    
    return {
        "total_subscribers": total_subscribers,
        "active_subscribers": active_subscribers,
        "inactive_subscribers": inactive_subscribers,
        "new_this_month": new_this_month,
        "expired_subscriptions": expired_subscriptions,
        "expiring_soon": expiring_soon,
        "subscribers_by_package": [
            {"package_id": item.package_id, "count": item.count}
            for item in subscribers_by_package
        ]
    }


@router.get("/routers-status")
async def get_routers_status(
    current_user: User = Depends(require_permission("reports")),
    db: Session = Depends(get_db)
):
    """حالة الراوترات"""
    # إجمالي الراوترات
    total_routers = db.query(Router).count()
    
    # حسب الحالة
    available_routers = db.query(Router).filter(Router.status == "available").count()
    assigned_routers = db.query(Router).filter(Router.status == "assigned").count()
    damaged_routers = db.query(Router).filter(Router.status == "damaged").count()
    
    # حسب الوضع
    new_routers = db.query(Router).filter(Router.condition == "new").count()
    used_routers = db.query(Router).filter(Router.condition == "used").count()
    
    # الراوترات المسلمة هذا الشهر
    current_month_start = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    assigned_this_month = db.query(Router).filter(
        Router.assigned_date >= current_month_start
    ).count()
    
    return {
        "total_routers": total_routers,
        "available_routers": available_routers,
        "assigned_routers": assigned_routers,
        "damaged_routers": damaged_routers,
        "new_routers": new_routers,
        "used_routers": used_routers,
        "assigned_this_month": assigned_this_month,
        "utilization_rate": (assigned_routers / total_routers * 100) if total_routers > 0 else 0
    }


@router.post("/custom")
async def create_custom_report(
    report_data: ReportCreate,
    current_user: User = Depends(require_permission("reports")),
    db: Session = Depends(get_db)
):
    """إنشاء تقرير مخصص"""
    # إنشاء التقرير
    report = Report(
        name=report_data.name,
        type=report_data.type,
        category=report_data.category,
        start_date=report_data.start_date,
        end_date=report_data.end_date,
        parameters=report_data.parameters,
        filters=report_data.filters,
        description=report_data.description,
        generated_by=current_user.id
    )
    
    # هنا يمكن إضافة منطق إنشاء البيانات حسب نوع التقرير
    # مثال بسيط:
    if report_data.category == "financial":
        transactions = db.query(Transaction).filter(
            and_(
                Transaction.transaction_date >= report_data.start_date,
                Transaction.transaction_date <= report_data.end_date
            )
        ).all()
        
        report.data = {
            "transactions": [
                {
                    "id": t.id,
                    "type": t.type,
                    "amount": t.amount,
                    "date": t.transaction_date.isoformat()
                }
                for t in transactions
            ]
        }
        
        report.summary = {
            "total_transactions": len(transactions),
            "total_amount": sum(t.amount for t in transactions)
        }
    
    db.add(report)
    db.commit()
    db.refresh(report)
    
    return report
