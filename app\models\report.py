"""
نماذج التقارير والإحصائيات
"""
from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, Text, ForeignKey, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.database import Base


class Report(Base):
    """نموذج التقرير"""
    __tablename__ = "reports"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(100), nullable=False)
    type = Column(String(50), nullable=False)  # daily, weekly, monthly, yearly, custom
    category = Column(String(50), nullable=False)  # financial, subscribers, inventory, cables
    
    # فترة التقرير
    start_date = Column(DateTime(timezone=True), nullable=False)
    end_date = Column(DateTime(timezone=True), nullable=False)
    
    # بيانات التقرير
    data = Column(JSON, nullable=True)  # بيانات التقرير بصيغة JSON
    summary = Column(JSON, nullable=True)  # ملخص التقرير
    
    # معلومات الإنشاء
    generated_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    generated_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # إعدادات التقرير
    parameters = Column(JSON, nullable=True)  # معاملات التقرير
    filters = Column(JSON, nullable=True)  # فلاتر التقرير
    
    # حالة التقرير
    status = Column(String(20), default="completed")  # pending, completed, failed
    file_path = Column(String(500), nullable=True)  # مسار ملف التقرير المحفوظ
    
    # معلومات إضافية
    description = Column(Text, nullable=True)
    notes = Column(Text, nullable=True)
    
    # تواريخ النظام
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # العلاقات
    generator = relationship("User")
    
    def __repr__(self):
        return f"<Report(name='{self.name}', type='{self.type}', category='{self.category}')>"


class DailyReport(Base):
    """نموذج التقرير اليومي"""
    __tablename__ = "daily_reports"
    
    id = Column(Integer, primary_key=True, index=True)
    date = Column(DateTime(timezone=True), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # إحصائيات المبيعات
    total_sales = Column(Float, default=0.0)
    total_subscriptions = Column(Integer, default=0)
    total_renewals = Column(Integer, default=0)
    total_router_sales = Column(Integer, default=0)
    
    # إحصائيات المشتركين
    new_subscribers = Column(Integer, default=0)
    active_subscribers = Column(Integer, default=0)
    expired_subscriptions = Column(Integer, default=0)
    
    # إحصائيات الراوترات
    routers_delivered = Column(Integer, default=0)
    routers_returned = Column(Integer, default=0)
    routers_available = Column(Integer, default=0)
    
    # إحصائيات الكبلات
    cables_assigned = Column(Integer, default=0)
    cables_used = Column(Integer, default=0)
    
    # إحصائيات مالية
    cash_income = Column(Float, default=0.0)
    card_income = Column(Float, default=0.0)
    total_expenses = Column(Float, default=0.0)
    net_income = Column(Float, default=0.0)
    
    # معلومات الصندوق
    opening_balance = Column(Float, default=0.0)
    closing_balance = Column(Float, default=0.0)
    
    # تواريخ النظام
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # العلاقات
    user = relationship("User")
    
    def __repr__(self):
        return f"<DailyReport(date='{self.date.date()}', sales={self.total_sales})>"


class MonthlyReport(Base):
    """نموذج التقرير الشهري"""
    __tablename__ = "monthly_reports"
    
    id = Column(Integer, primary_key=True, index=True)
    year = Column(Integer, nullable=False)
    month = Column(Integer, nullable=False)
    
    # إحصائيات شهرية
    total_revenue = Column(Float, default=0.0)
    total_expenses = Column(Float, default=0.0)
    net_profit = Column(Float, default=0.0)
    
    # إحصائيات المشتركين
    new_subscribers_count = Column(Integer, default=0)
    total_subscribers = Column(Integer, default=0)
    churn_rate = Column(Float, default=0.0)  # معدل الإلغاء
    
    # إحصائيات الخدمات
    subscription_revenue = Column(Float, default=0.0)
    router_sales_revenue = Column(Float, default=0.0)
    service_revenue = Column(Float, default=0.0)
    
    # إحصائيات التكاليف
    equipment_costs = Column(Float, default=0.0)
    operational_costs = Column(Float, default=0.0)
    salary_costs = Column(Float, default=0.0)
    
    # معلومات الإنشاء
    generated_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    generated_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # تواريخ النظام
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # العلاقات
    generator = relationship("User")
    
    def __repr__(self):
        return f"<MonthlyReport(year={self.year}, month={self.month}, revenue={self.total_revenue})>"


class SystemLog(Base):
    """نموذج سجل النظام"""
    __tablename__ = "system_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    
    # معلومات السجل
    action = Column(String(100), nullable=False)
    category = Column(String(50), nullable=False)  # auth, transaction, system, error
    level = Column(String(20), nullable=False)  # info, warning, error, critical
    
    # تفاصيل السجل
    message = Column(Text, nullable=False)
    details = Column(JSON, nullable=True)
    
    # معلومات إضافية
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(String(500), nullable=True)
    
    # تواريخ النظام
    timestamp = Column(DateTime(timezone=True), server_default=func.now())
    
    # العلاقات
    user = relationship("User")
    
    def __repr__(self):
        return f"<SystemLog(action='{self.action}', level='{self.level}')>"
