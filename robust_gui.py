#!/usr/bin/env python3
"""
واجهة مقاومة للأخطاء - تعمل في جميع الحالات
"""
import sys
import os
import asyncio
from pathlib import Path

def setup_environment():
    """إعداد البيئة"""
    try:
        # إضافة المسار الحالي إلى Python path
        current_dir = str(Path.cwd())
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)

        print("✅ تم إعداد البيئة")
        return True

    except Exception as e:
        print(f"⚠️ تحذير في إعداد البيئة: {e}")
        return True

def check_pyqt():
    """فحص توفر PyQt6"""
    try:
        import PyQt6
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt
        print("✅ PyQt6 متوفر")
        return True
    except ImportError as e:
        print(f"❌ PyQt6 غير متوفر: {e}")
        print("📦 يرجى تثبيت PyQt6:")
        print("   pip install PyQt6")
        return False

def check_pyqt():
    """فحص توفر PyQt6"""
    try:
        import PyQt6
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt
        print("✅ PyQt6 متوفر")
        return True
    except ImportError as e:
        print(f"❌ PyQt6 غير متوفر: {e}")
        print("📦 يرجى تثبيت PyQt6:")
        print("   pip install PyQt6")
        return False

async def setup_database():
    """إعداد قاعدة البيانات"""
    try:
        from app.core.config import create_directories
        from app.db.database import init_db
        
        print("🗂️ إنشاء المجلدات...")
        create_directories()
        
        print("🗄️ تهيئة قاعدة البيانات...")
        await init_db()
        
        print("✅ تم إعداد قاعدة البيانات")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
        return False

def create_simple_gui():
    """إنشاء واجهة بسيطة في حالة فشل الواجهة الرئيسية"""
    try:
        from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, 
                                   QWidget, QLabel, QPushButton, QMessageBox)
        from PyQt6.QtCore import Qt
        from PyQt6.QtGui import QFont
        
        class SimpleMainWindow(QMainWindow):
            def __init__(self):
                super().__init__()
                self.setWindowTitle("تطبيق المحاسبة - وضع الطوارئ")
                self.setGeometry(100, 100, 600, 400)
                
                # الواجهة الرئيسية
                central_widget = QWidget()
                self.setCentralWidget(central_widget)
                
                layout = QVBoxLayout(central_widget)
                layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
                
                # العنوان
                title = QLabel("تطبيق المحاسبة")
                title.setAlignment(Qt.AlignmentFlag.AlignCenter)
                title.setFont(QFont("Arial", 24, QFont.Weight.Bold))
                layout.addWidget(title)
                
                # رسالة الحالة
                status = QLabel("التطبيق يعمل في وضع الطوارئ")
                status.setAlignment(Qt.AlignmentFlag.AlignCenter)
                status.setFont(QFont("Arial", 14))
                layout.addWidget(status)
                
                # معلومات
                info = QLabel("""
                الواجهة الرئيسية غير متوفرة حالياً
                يمكنك استخدام واجهة الويب على:
                http://localhost:8000/docs
                """)
                info.setAlignment(Qt.AlignmentFlag.AlignCenter)
                info.setFont(QFont("Arial", 12))
                layout.addWidget(info)
                
                # أزرار
                btn_web = QPushButton("فتح واجهة الويب")
                btn_web.clicked.connect(self.open_web_interface)
                layout.addWidget(btn_web)
                
                btn_exit = QPushButton("خروج")
                btn_exit.clicked.connect(self.close)
                layout.addWidget(btn_exit)
            
            def open_web_interface(self):
                """فتح واجهة الويب"""
                try:
                    import webbrowser
                    webbrowser.open("http://localhost:8000/docs")
                except Exception as e:
                    QMessageBox.warning(self, "خطأ", f"لا يمكن فتح المتصفح: {e}")
        
        return SimpleMainWindow
        
    except Exception as e:
        print(f"❌ فشل في إنشاء الواجهة البسيطة: {e}")
        return None

def run_gui():
    """تشغيل الواجهة"""
    print("🚀 بدء تشغيل الواجهة المقاومة للأخطاء")
    print("=" * 50)
    
    # إعداد البيئة
    setup_environment()
    
    # فحص PyQt6
    if not check_pyqt():
        print("❌ لا يمكن تشغيل الواجهة بدون PyQt6")
        input("اضغط Enter للخروج...")
        return 1
    
    # إعداد قاعدة البيانات
    try:
        db_ready = asyncio.run(setup_database())
    except Exception as e:
        print(f"⚠️ تحذير: {e}")
        db_ready = False
    
    # إنشاء التطبيق
    try:
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt
        
        app = QApplication(sys.argv)
        app.setApplicationName("تطبيق المحاسبة")
        app.setApplicationVersion("1.0.0")
        
        # تطبيق الستايل العربي
        app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        
        # تطبيق الأنماط
        app.setStyleSheet("""
        QApplication {
            font-family: 'Segoe UI', 'Tahoma', 'Arial';
            font-size: 11pt;
        }
        
        QMainWindow {
            background-color: #f5f5f5;
        }
        
        QLabel {
            color: #2c3e50;
            margin: 10px;
        }
        
        QPushButton {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-size: 12pt;
            margin: 5px;
        }
        
        QPushButton:hover {
            background-color: #2980b9;
        }
        
        QPushButton:pressed {
            background-color: #21618c;
        }
        """)
        
        # محاولة تحميل الواجهة الرئيسية
        window = None
        try:
            from gui.windows.main_window import MainWindow
            window = MainWindow()
            print("✅ تم تحميل الواجهة الرئيسية")
        except Exception as e:
            print(f"⚠️ فشل في تحميل الواجهة الرئيسية: {e}")
            print("🔄 تشغيل الواجهة البسيطة...")
            
            SimpleMainWindow = create_simple_gui()
            if SimpleMainWindow:
                window = SimpleMainWindow()
                print("✅ تم تحميل الواجهة البسيطة")
            else:
                print("❌ فشل في إنشاء أي واجهة")
                return 1
        
        if window:
            # عرض النافذة
            window.show()
            
            print("=" * 50)
            print(f"🖥️ الواجهة جاهزة")
            print(f"📊 حالة النظام:")
            print(f"   • قاعدة البيانات: {'✅ جاهزة' if db_ready else '❌ غير متوفرة'}")
            print(f"   • الواجهة: ✅ تعمل")
            print("=" * 50)
            
            # تشغيل حلقة الأحداث
            return app.exec()
        else:
            print("❌ لم يتم إنشاء أي نافذة")
            return 1
            
    except Exception as e:
        print(f"❌ خطأ في تشغيل الواجهة: {e}")
        return 1

def main():
    """الدالة الرئيسية"""
    try:
        return run_gui()
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الواجهة بواسطة المستخدم")
        return 0
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        print("🔄 محاولة إعادة التشغيل...")
        try:
            return run_gui()
        except:
            print("❌ فشل في إعادة التشغيل")
            input("اضغط Enter للخروج...")
            return 1

if __name__ == "__main__":
    sys.exit(main())
