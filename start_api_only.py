#!/usr/bin/env python3
"""
تشغيل خادم API فقط
"""
import asyncio
import uvicorn
import sys
import os
from pathlib import Path

def setup_environment():
    """إعداد البيئة"""
    try:
        # إضافة المسار الحالي
        current_dir = str(Path.cwd())
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        print("✅ تم إعداد البيئة")
        return True
        
    except Exception as e:
        print(f"⚠️ تحذير في إعداد البيئة: {e}")
        return True

async def setup_database():
    """إعداد قاعدة البيانات"""
    try:
        from app.core.config import create_directories, settings
        from app.db.database import init_db
        
        print("🗂️ إنشاء المجلدات...")
        create_directories()
        
        print("🗄️ تهيئة قاعدة البيانات...")
        await init_db()
        
        print("✅ تم إعداد قاعدة البيانات")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إعداد قاعدة البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تشغيل خادم API")
    print("=" * 50)
    
    # إعداد البيئة
    setup_environment()
    
    # إعداد قاعدة البيانات
    try:
        db_ready = asyncio.run(setup_database())
    except Exception as e:
        print(f"⚠️ تحذير: {e}")
        db_ready = False
    
    if not db_ready:
        print("⚠️ تحذير: قاعدة البيانات غير متوفرة")
        print("💡 بعض الوظائف قد لا تعمل بشكل صحيح")
    
    # تحميل التطبيق
    try:
        from app.api.main import app
        print("✅ تم تحميل تطبيق API")
    except Exception as e:
        print(f"❌ فشل في تحميل التطبيق: {e}")
        return 1
    
    # معلومات الخادم
    port = 8000
    print("=" * 50)
    print(f"🌐 خادم API جاهز!")
    print(f"📍 الخادم متاح على:")
    print(f"   • http://localhost:{port}")
    print(f"   • http://127.0.0.1:{port}")
    print(f"📚 وثائق API: http://localhost:{port}/docs")
    print(f"🔑 بيانات تسجيل الدخول:")
    print(f"   • اسم المستخدم: admin")
    print(f"   • كلمة المرور: admin123")
    print("=" * 50)
    
    # تشغيل الخادم
    try:
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=port,
            log_level="info"
        )
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
        return 1

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الخادم بواسطة المستخدم")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ عام: {e}")
        input("اضغط Enter للخروج...")
        sys.exit(1)
