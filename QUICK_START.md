# 🚀 دليل التشغيل السريع

## ⚡ التشغيل الفوري (أسهل طريقة)

### 🖱️ الطريقة الأولى: النقر المزدوج
```
انقر مرتين على ملف: start.bat
```

### 💻 الطريقة الثانية: سطر الأوامر
```bash
python run_robust.py
```

## 🛠️ إذا واجهت مشاكل

### 1️⃣ تثبيت Python (إذا لم يكن مثبت)
- احمل Python من: https://python.org/downloads
- اختر إصدار 3.8 أو أحدث
- ✅ تأكد من تحديد "Add Python to PATH"

### 2️⃣ تثبيت المتطلبات الأساسية فقط
```bash
pip install fastapi uvicorn sqlalchemy pydantic PyQt6
```

### 3️⃣ إذا فشل تثبيت PyQt6
```bash
# جرب هذه البدائل:
pip install PyQt6 --no-cache-dir
# أو
pip install PyQt6 --user
# أو
conda install pyqt
```

### 4️⃣ تشغيل الخادم فقط (بدون واجهة)
```bash
python robust_server.py
```
ثم افتح: http://localhost:8000/docs

## 🌐 العمل مع مشاكل الشبكة

### 🔒 مع البروكسي/VPN
التطبيق يعمل تلقائياً مع:
- ✅ البروكسي المؤسسي
- ✅ VPN
- ✅ جدران الحماية
- ✅ الشبكات المحدودة

### 📴 بدون إنترنت
التطبيق يعمل بالكامل بدون إنترنت:
- ✅ جميع الوظائف متاحة
- ✅ قاعدة البيانات محلية
- ❌ فقط المزامنة مع Google Drive معطلة

## 🎯 أوضاع التشغيل

### 1. التشغيل الكامل
```bash
python run_robust.py
# اختر: 1
```
- خادم API + واجهة سطح المكتب

### 2. خادم فقط
```bash
python run_robust.py
# اختر: 2
```
- للاستخدام عبر الشبكة أو المتصفح

### 3. واجهة فقط
```bash
python run_robust.py
# اختر: 3
```
- لاستخدام سطح المكتب فقط

### 4. فحص النظام
```bash
python run_robust.py
# اختر: 4
```
- تشخيص المشاكل

## 🔧 حل المشاكل الشائعة

### ❌ "python غير معروف"
```bash
# Windows:
py run_robust.py
# أو
python3 run_robust.py
```

### ❌ "pip غير معروف"
```bash
python -m pip install fastapi uvicorn sqlalchemy pydantic
```

### ❌ "المنفذ 8000 مشغول"
التطبيق يجد منفذ متاح تلقائياً (8001, 8002, إلخ)

### ❌ مشاكل PyQt6
```bash
# استخدم الخادم فقط:
python robust_server.py
# ثم افتح: http://localhost:8000/docs
```

### ❌ مشاكل Google Drive
```bash
# التطبيق يعمل بدون Google Drive
# فقط المزامنة ستكون معطلة
```

## 📱 الوصول للتطبيق

### 🖥️ واجهة سطح المكتب
تظهر تلقائياً عند التشغيل

### 🌐 واجهة الويب
- **الرئيسية**: http://localhost:8000
- **وثائق API**: http://localhost:8000/docs
- **واجهة تفاعلية**: http://localhost:8000/redoc

### 🔑 بيانات تسجيل الدخول
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `admin123`

## 🎉 نصائح للاستخدام الأمثل

### 🚀 للأداء الأفضل
1. شغل التطبيق كمدير (Run as Administrator)
2. أضف مجلد التطبيق لاستثناءات مكافح الفيروسات
3. استخدم SSD إذا متوفر

### 🔒 للأمان
1. غير كلمة مرور admin بعد أول تسجيل دخول
2. فعّل النسخ الاحتياطي التلقائي
3. احتفظ بنسخة من مجلد data/

### 🌐 للعمل عبر الشبكة
1. شغل الخادم على الجهاز الرئيسي
2. الأجهزة الأخرى تصل عبر: http://IP:8000
3. تأكد من فتح المنفذ في الجدار الناري

## 📞 الحصول على المساعدة

### 🔍 تشخيص المشاكل
```bash
python run_robust.py
# اختر: 4 (فحص النظام)
```

### 📋 معلومات النظام
- إصدار Python: `python --version`
- المكتبات المثبتة: `pip list`
- حالة الشبكة: يظهر في فحص النظام

### 🆘 في حالة الطوارئ
إذا لم يعمل شيء، جرب:
```bash
# تشغيل مبسط جداً
python -c "
import uvicorn
from app.api.main import app
uvicorn.run(app, host='0.0.0.0', port=8000)
"
```

---

## 🎯 الخلاصة

التطبيق مصمم ليعمل في **جميع الحالات**:
- ✅ مع أو بدون إنترنت
- ✅ مع أو بدون بروكسي/VPN  
- ✅ مع أو بدون Google Drive
- ✅ حتى مع مشاكل الشبكة أو النظام

**أسهل طريقة**: انقر مرتين على `start.bat` وستعمل! 🎉
