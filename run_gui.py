#!/usr/bin/env python3
"""
تشغيل واجهة سطح المكتب فقط
"""
import sys
import asyncio
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt

from app.core.config import settings, create_directories
from app.db.database import init_db
from gui.windows.main_window import MainWindow


async def initialize_app():
    """تهيئة التطبيق"""
    print(f"🚀 بدء تشغيل واجهة {settings.app_name} v{settings.app_version}")
    
    # إنشاء المجلدات المطلوبة
    create_directories()
    print("✅ تم إنشاء المجلدات المطلوبة")
    
    # تهيئة قاعدة البيانات
    await init_db()
    print("✅ تم تهيئة قاعدة البيانات")
    
    print("✅ تم تهيئة التطبيق بنجاح")


def main():
    """الدالة الرئيسية"""
    # تهيئة التطبيق
    try:
        asyncio.run(initialize_app())
    except Exception as e:
        print(f"❌ خطأ في تهيئة التطبيق: {e}")
        return 1
    
    # إنشاء تطبيق PyQt
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
    
    # تطبيق الأنماط العامة
    app.setStyleSheet("""
    QApplication {
        font-family: 'Segoe UI', 'Tahoma', 'Arial';
        font-size: 10pt;
    }
    
    QWidget {
        background-color: #f5f5f5;
    }
    
    QMainWindow {
        background-color: #ffffff;
    }
    
    QMenuBar {
        background-color: #2c3e50;
        color: white;
        border: none;
        padding: 4px;
    }
    
    QMenuBar::item {
        background-color: transparent;
        padding: 8px 12px;
        border-radius: 4px;
    }
    
    QMenuBar::item:selected {
        background-color: #34495e;
    }
    
    QMenu {
        background-color: white;
        border: 1px solid #bdc3c7;
        border-radius: 4px;
        padding: 4px;
    }
    
    QMenu::item {
        padding: 8px 20px;
        border-radius: 4px;
    }
    
    QMenu::item:selected {
        background-color: #3498db;
        color: white;
    }
    
    QStatusBar {
        background-color: #ecf0f1;
        border-top: 1px solid #bdc3c7;
        padding: 4px;
    }
    
    QMessageBox {
        background-color: white;
    }
    
    QMessageBox QLabel {
        color: #2c3e50;
    }
    
    QMessageBox QPushButton {
        background-color: #3498db;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        min-width: 80px;
    }
    
    QMessageBox QPushButton:hover {
        background-color: #2980b9;
    }
    
    QMessageBox QPushButton:pressed {
        background-color: #21618c;
    }
    """)
    
    # إنشاء النافذة الرئيسية
    try:
        window = MainWindow()
        
        # عرض النافذة
        window.show()
        
        print("✅ تم تشغيل واجهة المستخدم")
        print("ℹ️ لاستخدام API، شغل run_server.py في نافذة منفصلة")
        
        # تشغيل حلقة الأحداث
        return app.exec()
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل واجهة المستخدم: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
