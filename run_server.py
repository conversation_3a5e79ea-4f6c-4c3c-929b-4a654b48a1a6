#!/usr/bin/env python3
"""
تشغيل الخادم فقط (بدون واجهة سطح المكتب)
"""
import asyncio
import uvicorn
from app.api.main import app
from app.core.config import settings, create_directories
from app.db.database import init_db
from sync.sync_service import sync_service


async def start_server():
    """بدء الخادم"""
    print(f"🚀 بدء تشغيل خادم {settings.app_name} v{settings.app_version}")
    
    # إنشاء المجلدات المطلوبة
    create_directories()
    print("✅ تم إنشاء المجلدات المطلوبة")
    
    # تهيئة قاعدة البيانات
    await init_db()
    print("✅ تم تهيئة قاعدة البيانات")
    
    # بدء خدمة المزامنة
    if not settings.offline_mode:
        try:
            sync_service.start()
            print("✅ تم بدء خدمة المزامنة")
        except Exception as e:
            print(f"⚠️ تحذير: لم يتم بدء خدمة المزامنة: {e}")
    else:
        print("ℹ️ الوضع غير المتصل مفعل - لن يتم بدء المزامنة")
    
    print("✅ تم تهيئة الخادم بنجاح")
    print(f"🌐 الخادم متاح على: http://{settings.api_host}:{settings.api_port}")
    print(f"📚 وثائق API: http://{settings.api_host}:{settings.api_port}/docs")
    
    # تشغيل الخادم
    config = uvicorn.Config(
        app,
        host=settings.api_host,
        port=settings.api_port,
        log_level="info",
        reload=settings.debug
    )
    
    server = uvicorn.Server(config)
    await server.serve()


if __name__ == "__main__":
    try:
        asyncio.run(start_server())
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الخادم بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")
    finally:
        # إيقاف خدمة المزامنة
        try:
            sync_service.stop()
            print("✅ تم إيقاف خدمة المزامنة")
        except:
            pass
