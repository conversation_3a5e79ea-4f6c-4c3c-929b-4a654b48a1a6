# سجل التغييرات

جميع التغييرات المهمة في هذا المشروع سيتم توثيقها في هذا الملف.

التنسيق مبني على [Keep a Changelog](https://keepachangelog.com/ar/1.0.0/)،
وهذا المشروع يتبع [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [غير منشور]

### مضاف
- ميزات جديدة قيد التطوير

### تم تغييره
- تحسينات على الميزات الموجودة

### مُصلح
- إصلاحات الأخطاء

## [1.0.0] - 2025-01-09

### مضاف
- **النظام الأساسي**
  - إعداد مشروع FastAPI مع SQLAlchemy
  - نظام مصادقة JWT مع أدوار المستخدمين
  - قاعدة بيانات SQLite مع نماذج شاملة
  - واجهة PyQt6 عربية بالكامل مع دعم RTL

- **إدارة المشتركين**
  - إضافة وتعديل وحذف المشتركين
  - إدارة الاشتراكات والباقات
  - تتبع تواريخ انتهاء الاشتراكات
  - تجديد الاشتراكات مع حساب المدد

- **إدارة الراوترات**
  - تتبع مخزون الراوترات
  - تسليم وإرجاع الراوترات
  - إدارة حالة الراوترات (متاح، مسلم، تالف)
  - ربط الراوترات بالمشتركين

- **النظام المالي**
  - تسجيل جميع المعاملات المالية
  - إدارة الصندوق اليومي (فتح/إغلاق)
  - دعم طرق دفع متعددة (نقدي، بطاقة، تحويل بنكي، شيك)
  - حساب الإجماليات والأرباح

- **إدارة الكبلات والعمال**
  - تتبع توزيع الكبلات على العمال
  - مراقبة استخدام الكبلات
  - تقارير أسبوعية للعمال
  - إدارة أنواع الكبلات المختلفة

- **إدارة المخزون**
  - تتبع المنتجات والمواد
  - إدارة حركة المخزون (دخول، خروج، تعديل)
  - تنبيهات المخزون المنخفض
  - تصنيف المنتجات والوحدات

- **نظام الطباعة**
  - طباعة الإيصالات (حراري 80mm و A4)
  - طباعة التقارير المالية
  - قوالب طباعة قابلة للتخصيص
  - دعم الطابعات المختلفة عبر أنظمة التشغيل

- **المزامنة والنسخ الاحتياطي**
  - مزامنة تلقائية مع Google Drive
  - نسخ احتياطي يومي مجدول
  - استعادة النسخ الاحتياطية
  - العمل في الوضع غير المتصل

- **التقارير والإحصائيات**
  - تقارير يومية شاملة
  - تقارير شهرية مفصلة
  - ملخص مالي لفترات مخصصة
  - إحصائيات المشتركين والراوترات

- **واجهة المستخدم**
  - لوحة تحكم شاملة مع إحصائيات فورية
  - واجهة عربية بالكامل مع دعم RTL
  - تصميم حديث وسهل الاستخدام
  - نوافذ منفصلة لكل وظيفة

- **API RESTful**
  - endpoints شاملة لجميع الوظائف
  - وثائق تفاعلية في `/docs`
  - دعم CORS للعمل عبر الشبكة
  - مصادقة JWT آمنة

- **الأمان والصلاحيات**
  - نظام صلاحيات متدرج (مدير، كاشير، مستخدم)
  - تشفير كلمات المرور باستخدام bcrypt
  - تتبع نشاط المستخدمين
  - حماية من SQL Injection

- **أدوات التطوير**
  - ملفات تشغيل متعددة (كامل، خادم فقط، واجهة فقط)
  - إعداد تلقائي للمتطلبات
  - نظام سجلات شامل
  - اختبارات وحدة

### الميزات التقنية
- **قاعدة البيانات**: SQLite مع SQLAlchemy ORM
- **الخادم**: FastAPI مع Uvicorn
- **الواجهة**: PyQt6 مع دعم العربية
- **المصادقة**: JWT مع Passlib
- **الطباعة**: ReportLab للـ PDF
- **المزامنة**: Google Drive API
- **الجدولة**: Schedule للمهام التلقائية

### ملفات الإعداد
- `.env.example` - قالب الإعدادات
- `google_credentials_example.json` - قالب بيانات Google Drive
- `requirements.txt` - متطلبات Python
- `setup.py` - إعداد التوزيع

### الوثائق
- `README.md` - دليل المستخدم الشامل
- `DEVELOPMENT.md` - دليل التطوير
- `GOOGLE_DRIVE_SETUP.md` - إعداد Google Drive
- `CHANGELOG.md` - سجل التغييرات

### أدوات التشغيل
- `main.py` - التطبيق الكامل
- `run.py` - تشغيل مع فحص المتطلبات
- `run_server.py` - الخادم فقط
- `run_gui.py` - الواجهة فقط

## [0.1.0] - 2025-01-01

### مضاف
- إعداد المشروع الأولي
- هيكل المجلدات الأساسي
- إعدادات التطوير الأولية

---

## أنواع التغييرات

- `مضاف` للميزات الجديدة
- `تم تغييره` للتغييرات في الميزات الموجودة
- `مُهمل` للميزات التي ستُزال قريباً
- `مُزال` للميزات المُزالة
- `مُصلح` لإصلاحات الأخطاء
- `أمان` في حالة الثغرات الأمنية
