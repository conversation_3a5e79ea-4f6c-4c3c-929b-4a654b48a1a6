# Essential Dependencies (مرن - يعمل مع إصدارات متعددة)
fastapi>=0.100.0
uvicorn[standard]>=0.20.0
sqlalchemy>=2.0.0
pydantic>=2.0.0

# Authentication & Security (اختياري)
python-jose[cryptography]>=3.3.0
passlib[bcrypt]>=1.7.4

# Configuration
pydantic-settings>=2.0.0
python-dotenv>=1.0.0
python-multipart>=0.0.6

# GUI Framework (اختياري)
PyQt6>=6.5.0

# Network & HTTP
requests>=2.25.0
httpx>=0.24.0

# Google Drive Integration (اختياري)
google-auth>=2.0.0
google-auth-oauthlib>=1.0.0
google-auth-httplib2>=0.1.0
google-api-python-client>=2.0.0

# Printing & PDF (اختياري)
reportlab>=3.6.0

# Task Scheduling (اختياري)
schedule>=1.2.0

# Development Tools (اختياري)
pytest>=7.0.0
black>=23.0.0
flake8>=6.0.0
openpyxl==3.1.2
pandas==2.1.3
jinja2==3.1.2

# Development
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
flake8==6.1.0
