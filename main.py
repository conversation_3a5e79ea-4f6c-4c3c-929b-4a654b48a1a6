#!/usr/bin/env python3
"""
تطبيق المحاسبة المتكامل - الملف الرئيسي
"""
import sys
import asyncio
import threading
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.config import settings, create_directories
from app.db.database import init_db
from sync.sync_service import sync_service


def run_api_server():
    """تشغيل خادم API في خيط منفصل"""
    import uvicorn
    from app.api.main import app
    
    uvicorn.run(
        app,
        host=settings.api_host,
        port=settings.api_port,
        log_level="info" if settings.debug else "warning"
    )


def run_gui_app():
    """تشغيل واجهة المستخدم الرسومية"""
    from PyQt6.QtWidgets import QApplication
    from PyQt6.QtCore import Qt
    from gui.windows.main_window import MainWindow

    app = QApplication(sys.argv)
    app.setApplicationName(settings.app_name)
    app.setApplicationVersion(settings.app_version)

    # تطبيق الستايل العربي
    app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
    
    # إنشاء النافذة الرئيسية
    main_window = MainWindow()
    main_window.show()
    
    return app.exec()


async def initialize_app():
    """تهيئة التطبيق"""
    print(f"🚀 بدء تشغيل {settings.app_name} v{settings.app_version}")

    # إنشاء المجلدات المطلوبة
    create_directories()
    print("✅ تم إنشاء المجلدات المطلوبة")

    # تهيئة قاعدة البيانات
    await init_db()
    print("✅ تم تهيئة قاعدة البيانات")

    # بدء خدمة المزامنة
    if not settings.offline_mode:
        try:
            sync_service.start()
            print("✅ تم بدء خدمة المزامنة")
        except Exception as e:
            print(f"⚠️ تحذير: لم يتم بدء خدمة المزامنة: {e}")
    else:
        print("ℹ️ الوضع غير المتصل مفعل - لن يتم بدء المزامنة")

    print("✅ تم تهيئة التطبيق بنجاح")


def main():
    """الدالة الرئيسية"""
    try:
        # تهيئة التطبيق
        asyncio.run(initialize_app())
        
        # تشغيل خادم API في خيط منفصل
        api_thread = threading.Thread(target=run_api_server, daemon=True)
        api_thread.start()
        print(f"🌐 تم تشغيل خادم API على http://{settings.api_host}:{settings.api_port}")
        
        # تشغيل واجهة المستخدم الرسومية
        print("🖥️ تشغيل واجهة المستخدم...")
        exit_code = run_gui_app()
        
        return exit_code
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
        return 0
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
