"""
واجهة لوحة التحكم
"""
import requests
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, 
    QPushButton, QFrame, QScrollArea, QMessageBox
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QFont

from app.core.config import settings


class StatCard(QFrame):
    """بطاقة إحصائية"""
    
    def __init__(self, title, value, icon, color="#667eea"):
        super().__init__()
        self.setObjectName("statCard")
        self.init_ui(title, value, icon, color)
    
    def init_ui(self, title, value, icon, color):
        """تهيئة واجهة البطاقة"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(10)
        
        # الأيقونة
        icon_label = QLabel(icon)
        icon_label.setObjectName("cardIcon")
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(icon_label)
        
        # القيمة
        value_label = QLabel(str(value))
        value_label.setObjectName("cardValue")
        value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(value_label)
        
        # العنوان
        title_label = QLabel(title)
        title_label.setObjectName("cardTitle")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setWordWrap(True)
        layout.addWidget(title_label)
        
        # تطبيق الأنماط
        self.setStyleSheet(f"""
        QFrame#statCard {{
            background: {color};
            border-radius: 10px;
            min-height: 120px;
            max-height: 150px;
        }}
        
        QLabel#cardIcon {{
            font-size: 32px;
            color: white;
        }}
        
        QLabel#cardValue {{
            font-size: 24px;
            font-weight: bold;
            color: white;
        }}
        
        QLabel#cardTitle {{
            font-size: 12px;
            color: rgba(255, 255, 255, 0.9);
        }}
        """)


class QuickActionButton(QPushButton):
    """زر إجراء سريع"""
    
    def __init__(self, text, icon, color="#4CAF50"):
        super().__init__(f"{icon} {text}")
        self.setObjectName("quickActionButton")
        self.apply_style(color)
    
    def apply_style(self, color):
        """تطبيق الأنماط"""
        self.setStyleSheet(f"""
        QPushButton#quickActionButton {{
            padding: 15px 20px;
            font-size: 14px;
            font-weight: bold;
            color: white;
            background: {color};
            border: none;
            border-radius: 8px;
            text-align: right;
        }}
        
        QPushButton#quickActionButton:hover {{
            background: {self.darken_color(color)};
        }}
        
        QPushButton#quickActionButton:pressed {{
            background: {self.darken_color(color, 0.8)};
        }}
        """)
    
    def darken_color(self, color, factor=0.9):
        """تغميق اللون"""
        # تحويل بسيط للون
        if color.startswith("#"):
            r = int(color[1:3], 16)
            g = int(color[3:5], 16)
            b = int(color[5:7], 16)
            
            r = int(r * factor)
            g = int(g * factor)
            b = int(b * factor)
            
            return f"#{r:02x}{g:02x}{b:02x}"
        return color


class DashboardWidget(QWidget):
    """واجهة لوحة التحكم"""
    
    def __init__(self, user, access_token):
        super().__init__()
        self.user = user
        self.access_token = access_token
        self.stats_data = {}
        
        self.init_ui()
        self.load_dashboard_data()
        
        # تحديث البيانات كل 30 ثانية
        self.timer = QTimer()
        self.timer.timeout.connect(self.load_dashboard_data)
        self.timer.start(30000)
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(20)
        
        # منطقة التمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        scroll_layout.setSpacing(20)
        
        # رسالة ترحيب
        self.create_welcome_section(scroll_layout)
        
        # الإحصائيات السريعة
        self.create_stats_section(scroll_layout)
        
        # الإجراءات السريعة
        self.create_quick_actions_section(scroll_layout)
        
        # التنبيهات والإشعارات
        self.create_alerts_section(scroll_layout)
        
        scroll_area.setWidget(scroll_widget)
        main_layout.addWidget(scroll_area)
    
    def create_welcome_section(self, layout):
        """إنشاء قسم الترحيب"""
        welcome_frame = QFrame()
        welcome_frame.setObjectName("welcomeFrame")
        welcome_layout = QVBoxLayout(welcome_frame)
        welcome_layout.setContentsMargins(20, 20, 20, 20)
        
        # رسالة الترحيب
        welcome_label = QLabel(f"مرحباً بك، {self.user['full_name']}")
        welcome_label.setObjectName("welcomeLabel")
        welcome_layout.addWidget(welcome_label)
        
        # معلومات إضافية
        info_label = QLabel(f"الدور: {self.user['role']} | آخر تسجيل دخول: اليوم")
        info_label.setObjectName("infoLabel")
        welcome_layout.addWidget(info_label)
        
        # تطبيق الأنماط
        welcome_frame.setStyleSheet("""
        QFrame#welcomeFrame {
            background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                stop:0 #667eea, stop:1 #764ba2);
            border-radius: 10px;
        }
        
        QLabel#welcomeLabel {
            font-size: 20px;
            font-weight: bold;
            color: white;
        }
        
        QLabel#infoLabel {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
        }
        """)
        
        layout.addWidget(welcome_frame)
    
    def create_stats_section(self, layout):
        """إنشاء قسم الإحصائيات"""
        stats_label = QLabel("الإحصائيات السريعة")
        stats_label.setObjectName("sectionTitle")
        layout.addWidget(stats_label)
        
        # شبكة الإحصائيات
        self.stats_grid = QGridLayout()
        self.stats_grid.setSpacing(15)
        
        # بطاقات الإحصائيات (ستملأ بالبيانات لاحقاً)
        self.stat_cards = {}
        
        stats_frame = QFrame()
        stats_frame.setLayout(self.stats_grid)
        layout.addWidget(stats_frame)
    
    def create_quick_actions_section(self, layout):
        """إنشاء قسم الإجراءات السريعة"""
        actions_label = QLabel("الإجراءات السريعة")
        actions_label.setObjectName("sectionTitle")
        layout.addWidget(actions_label)
        
        # شبكة الإجراءات
        actions_grid = QGridLayout()
        actions_grid.setSpacing(15)
        
        # الإجراءات المتاحة حسب الصلاحيات
        actions = []
        
        if self.user_can_access("subscribers"):
            actions.append(("إضافة مشترك جديد", "👤", "#4CAF50", self.add_subscriber))
            actions.append(("تجديد اشتراك", "🔄", "#2196F3", self.renew_subscription))
        
        if self.user_can_access("routers"):
            actions.append(("تسليم راوتر", "📡", "#FF9800", self.assign_router))
        
        if self.user_can_access("transactions"):
            actions.append(("إضافة معاملة", "💰", "#9C27B0", self.add_transaction))
        
        if self.user_can_access("reports"):
            actions.append(("تقرير يومي", "📊", "#607D8B", self.daily_report))
        
        # إضافة الأزرار
        row, col = 0, 0
        for text, icon, color, callback in actions:
            button = QuickActionButton(text, icon, color)
            button.clicked.connect(callback)
            actions_grid.addWidget(button, row, col)
            
            col += 1
            if col >= 3:  # 3 أزرار في كل صف
                col = 0
                row += 1
        
        actions_frame = QFrame()
        actions_frame.setLayout(actions_grid)
        layout.addWidget(actions_frame)
    
    def create_alerts_section(self, layout):
        """إنشاء قسم التنبيهات"""
        alerts_label = QLabel("التنبيهات والإشعارات")
        alerts_label.setObjectName("sectionTitle")
        layout.addWidget(alerts_label)
        
        self.alerts_frame = QFrame()
        self.alerts_frame.setObjectName("alertsFrame")
        self.alerts_layout = QVBoxLayout(self.alerts_frame)
        self.alerts_layout.setContentsMargins(15, 15, 15, 15)
        
        # رسالة افتراضية
        no_alerts_label = QLabel("لا توجد تنبيهات جديدة")
        no_alerts_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        no_alerts_label.setStyleSheet("color: #666; font-style: italic;")
        self.alerts_layout.addWidget(no_alerts_label)
        
        self.alerts_frame.setStyleSheet("""
        QFrame#alertsFrame {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        """)
        
        layout.addWidget(self.alerts_frame)
        
        # تطبيق أنماط الأقسام
        layout.parent().setStyleSheet("""
        QLabel#sectionTitle {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin: 10px 0;
        }
        """)
    
    def load_dashboard_data(self):
        """تحميل بيانات لوحة التحكم"""
        try:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            
            # تحميل الإحصائيات
            self.load_subscribers_stats(headers)
            self.load_routers_stats(headers)
            self.load_financial_stats(headers)
            
            # تحديث بطاقات الإحصائيات
            self.update_stat_cards()
            
        except Exception as e:
            print(f"خطأ في تحميل بيانات لوحة التحكم: {e}")
    
    def load_subscribers_stats(self, headers):
        """تحميل إحصائيات المشتركين"""
        try:
            url = f"http://{settings.api_host}:{settings.api_port}/api/reports/subscribers-analysis"
            response = requests.get(url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                self.stats_data["subscribers"] = response.json()
        except:
            pass
    
    def load_routers_stats(self, headers):
        """تحميل إحصائيات الراوترات"""
        try:
            url = f"http://{settings.api_host}:{settings.api_port}/api/reports/routers-status"
            response = requests.get(url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                self.stats_data["routers"] = response.json()
        except:
            pass
    
    def load_financial_stats(self, headers):
        """تحميل الإحصائيات المالية"""
        try:
            from datetime import date
            today = date.today()
            url = f"http://{settings.api_host}:{settings.api_port}/api/transactions/daily-summary/{today}"
            response = requests.get(url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                self.stats_data["financial"] = response.json()
        except:
            pass
    
    def update_stat_cards(self):
        """تحديث بطاقات الإحصائيات"""
        # مسح البطاقات الموجودة
        for card in self.stat_cards.values():
            card.setParent(None)
        self.stat_cards.clear()
        
        # إضافة البطاقات الجديدة
        cards_data = []
        
        # إحصائيات المشتركين
        if "subscribers" in self.stats_data:
            data = self.stats_data["subscribers"]
            cards_data.extend([
                ("إجمالي المشتركين", data.get("total_subscribers", 0), "👥", "#4CAF50"),
                ("المشتركين النشطين", data.get("active_subscribers", 0), "✅", "#2196F3"),
                ("اشتراكات منتهية", data.get("expired_subscriptions", 0), "⚠️", "#FF5722"),
            ])
        
        # إحصائيات الراوترات
        if "routers" in self.stats_data:
            data = self.stats_data["routers"]
            cards_data.extend([
                ("راوترات متاحة", data.get("available_routers", 0), "📡", "#FF9800"),
                ("راوترات مسلمة", data.get("assigned_routers", 0), "📤", "#9C27B0"),
            ])
        
        # الإحصائيات المالية
        if "financial" in self.stats_data:
            data = self.stats_data["financial"]
            cards_data.extend([
                ("مبيعات اليوم", f"{data.get('total_income', 0):.0f}", "💰", "#607D8B"),
                ("صافي الربح", f"{data.get('net_amount', 0):.0f}", "📈", "#795548"),
            ])
        
        # إضافة البطاقات إلى الشبكة
        row, col = 0, 0
        for title, value, icon, color in cards_data:
            card = StatCard(title, value, icon, color)
            self.stat_cards[title] = card
            self.stats_grid.addWidget(card, row, col)
            
            col += 1
            if col >= 4:  # 4 بطاقات في كل صف
                col = 0
                row += 1
    
    def user_can_access(self, resource):
        """التحقق من صلاحية المستخدم"""
        # منطق بسيط للصلاحيات
        if self.user["role"] == "admin":
            return True
        
        user_permissions = {
            "cashier": ["subscribers", "routers", "transactions", "reports"],
            "user": ["subscribers", "routers", "reports"]
        }
        
        return resource in user_permissions.get(self.user["role"], [])
    
    # دوال الإجراءات السريعة
    def add_subscriber(self):
        QMessageBox.information(self, "إضافة مشترك", "سيتم فتح نافذة إضافة مشترك جديد")
    
    def renew_subscription(self):
        QMessageBox.information(self, "تجديد اشتراك", "سيتم فتح نافذة تجديد الاشتراك")
    
    def assign_router(self):
        QMessageBox.information(self, "تسليم راوتر", "سيتم فتح نافذة تسليم راوتر")
    
    def add_transaction(self):
        QMessageBox.information(self, "إضافة معاملة", "سيتم فتح نافذة إضافة معاملة جديدة")
    
    def daily_report(self):
        QMessageBox.information(self, "تقرير يومي", "سيتم فتح التقرير اليومي")
