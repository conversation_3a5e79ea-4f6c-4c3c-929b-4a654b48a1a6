"""
مسارات إدارة الراوترات
"""
from typing import List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel

from app.db.database import get_db
from app.services.auth import get_current_active_user, require_permission
from app.models.user import User
from app.models.router import Router
from app.models.subscriber import Subscriber

router = APIRouter()


class RouterCreate(BaseModel):
    """نموذج إنشاء راوتر"""
    serial_number: str
    model: str
    brand: str
    purchase_price: float
    purchase_date: Optional[datetime] = None
    supplier: Optional[str] = None
    condition: str = "new"
    mac_address: Optional[str] = None
    firmware_version: Optional[str] = None
    warranty_end: Optional[datetime] = None
    notes: Optional[str] = None


class RouterUpdate(BaseModel):
    """نموذج تحديث راوتر"""
    model: Optional[str] = None
    brand: Optional[str] = None
    purchase_price: Optional[float] = None
    purchase_date: Optional[datetime] = None
    supplier: Optional[str] = None
    status: Optional[str] = None
    condition: Optional[str] = None
    mac_address: Optional[str] = None
    firmware_version: Optional[str] = None
    warranty_end: Optional[datetime] = None
    notes: Optional[str] = None


class RouterResponse(BaseModel):
    """استجابة الراوتر"""
    id: int
    serial_number: str
    model: str
    brand: str
    purchase_price: float
    purchase_date: Optional[datetime]
    supplier: Optional[str]
    status: str
    condition: str
    assigned_to: Optional[int]
    assigned_date: Optional[datetime]
    mac_address: Optional[str]
    firmware_version: Optional[str]
    warranty_end: Optional[datetime]
    notes: Optional[str]
    created_at: datetime
    
    # معلومات إضافية
    subscriber_name: Optional[str] = None
    days_in_use: Optional[int] = None

    class Config:
        from_attributes = True


class RouterAssignRequest(BaseModel):
    """طلب تسليم راوتر"""
    subscriber_id: int
    notes: Optional[str] = None


class RouterReturnRequest(BaseModel):
    """طلب إرجاع راوتر"""
    reason: Optional[str] = None
    condition: str = "used"
    notes: Optional[str] = None


@router.get("/", response_model=List[RouterResponse])
async def get_routers(
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = Query(None, description="فلترة حسب الحالة"),
    condition: Optional[str] = Query(None, description="فلترة حسب الوضع"),
    brand: Optional[str] = Query(None, description="فلترة حسب الماركة"),
    current_user: User = Depends(require_permission("routers")),
    db: Session = Depends(get_db)
):
    """الحصول على قائمة الراوترات"""
    query = db.query(Router)
    
    if status:
        query = query.filter(Router.status == status)
    
    if condition:
        query = query.filter(Router.condition == condition)
    
    if brand:
        query = query.filter(Router.brand == brand)
    
    routers = query.offset(skip).limit(limit).all()
    
    # إضافة معلومات إضافية
    result = []
    for router_item in routers:
        router_data = RouterResponse.from_orm(router_item)
        
        # اسم المشترك
        if router_item.subscriber:
            router_data.subscriber_name = router_item.subscriber.name
        
        # أيام الاستخدام
        if router_item.assigned_date:
            if router_item.returned_date:
                router_data.days_in_use = (router_item.returned_date - router_item.assigned_date).days
            else:
                router_data.days_in_use = (datetime.now() - router_item.assigned_date).days
        
        result.append(router_data)
    
    return result


@router.get("/available", response_model=List[RouterResponse])
async def get_available_routers(
    current_user: User = Depends(require_permission("routers")),
    db: Session = Depends(get_db)
):
    """الحصول على الراوترات المتاحة"""
    routers = db.query(Router).filter(Router.status == "available").all()
    return [RouterResponse.from_orm(router_item) for router_item in routers]


@router.get("/{router_id}", response_model=RouterResponse)
async def get_router(
    router_id: int,
    current_user: User = Depends(require_permission("routers")),
    db: Session = Depends(get_db)
):
    """الحصول على راوتر محدد"""
    router_item = db.query(Router).filter(Router.id == router_id).first()
    if not router_item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الراوتر غير موجود"
        )
    
    router_data = RouterResponse.from_orm(router_item)
    
    # إضافة معلومات إضافية
    if router_item.subscriber:
        router_data.subscriber_name = router_item.subscriber.name
    
    if router_item.assigned_date:
        if router_item.returned_date:
            router_data.days_in_use = (router_item.returned_date - router_item.assigned_date).days
        else:
            router_data.days_in_use = (datetime.now() - router_item.assigned_date).days
    
    return router_data


@router.post("/", response_model=RouterResponse)
async def create_router(
    router_data: RouterCreate,
    current_user: User = Depends(require_permission("routers")),
    db: Session = Depends(get_db)
):
    """إنشاء راوتر جديد"""
    # التحقق من عدم وجود الرقم التسلسلي
    existing_router = db.query(Router).filter(Router.serial_number == router_data.serial_number).first()
    if existing_router:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="الرقم التسلسلي موجود بالفعل"
        )
    
    # إنشاء الراوتر
    router_item = Router(**router_data.dict())
    
    db.add(router_item)
    db.commit()
    db.refresh(router_item)
    
    return RouterResponse.from_orm(router_item)


@router.put("/{router_id}", response_model=RouterResponse)
async def update_router(
    router_id: int,
    router_data: RouterUpdate,
    current_user: User = Depends(require_permission("routers")),
    db: Session = Depends(get_db)
):
    """تحديث راوتر"""
    router_item = db.query(Router).filter(Router.id == router_id).first()
    if not router_item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الراوتر غير موجود"
        )
    
    # تحديث البيانات
    update_data = router_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(router_item, field, value)
    
    db.commit()
    db.refresh(router_item)
    
    return RouterResponse.from_orm(router_item)


@router.post("/{router_id}/assign")
async def assign_router(
    router_id: int,
    assign_data: RouterAssignRequest,
    current_user: User = Depends(require_permission("routers")),
    db: Session = Depends(get_db)
):
    """تسليم راوتر لمشترك"""
    router_item = db.query(Router).filter(Router.id == router_id).first()
    if not router_item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الراوتر غير موجود"
        )
    
    if not router_item.is_available:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="الراوتر غير متاح للتسليم"
        )
    
    # التحقق من المشترك
    subscriber = db.query(Subscriber).filter(Subscriber.id == assign_data.subscriber_id).first()
    if not subscriber:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="المشترك غير موجود"
        )
    
    # تسليم الراوتر
    router_item.assign_to_subscriber(assign_data.subscriber_id, current_user.id)
    subscriber.router_id = router_id
    
    if assign_data.notes:
        router_item.notes = assign_data.notes
    
    # إنشاء معاملة التسليم
    from app.models.transaction import Transaction
    transaction = Transaction(
        transaction_number=f"RTR-{datetime.now().strftime('%Y%m%d%H%M%S')}",
        type="router_sale",
        amount=router_item.purchase_price,  # يمكن تعديل السعر حسب الحاجة
        payment_method="cash",
        description=f"تسليم راوتر {router_item.serial_number}",
        notes=assign_data.notes,
        subscriber_id=assign_data.subscriber_id,
        router_id=router_id,
        user_id=current_user.id
    )
    
    db.add(transaction)
    db.commit()
    
    return {
        "message": "تم تسليم الراوتر بنجاح",
        "router_serial": router_item.serial_number,
        "subscriber_name": subscriber.name,
        "transaction_number": transaction.transaction_number
    }


@router.post("/{router_id}/return")
async def return_router(
    router_id: int,
    return_data: RouterReturnRequest,
    current_user: User = Depends(require_permission("routers")),
    db: Session = Depends(get_db)
):
    """إرجاع راوتر"""
    router_item = db.query(Router).filter(Router.id == router_id).first()
    if not router_item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الراوتر غير موجود"
        )
    
    if not router_item.is_assigned:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="الراوتر غير مسلم لأي مشترك"
        )
    
    # إرجاع الراوتر
    subscriber_id = router_item.assigned_to
    router_item.return_router(return_data.reason, return_data.condition)
    
    # إزالة الراوتر من المشترك
    if subscriber_id:
        subscriber = db.query(Subscriber).filter(Subscriber.id == subscriber_id).first()
        if subscriber:
            subscriber.router_id = None
    
    if return_data.notes:
        router_item.notes = return_data.notes
    
    db.commit()
    
    return {
        "message": "تم إرجاع الراوتر بنجاح",
        "router_serial": router_item.serial_number,
        "return_condition": return_data.condition
    }
