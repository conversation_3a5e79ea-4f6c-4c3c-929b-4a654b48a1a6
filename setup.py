"""
إعداد التطبيق للتوزيع
"""
from setuptools import setup, find_packages
import os

# قراءة ملف README
with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

# قراءة المتطلبات
with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="accounting-app",
    version="1.0.0",
    author="فريق التطوير",
    author_email="<EMAIL>",
    description="تطبيق محاسبة متكامل لإدارة المشتركين والعمليات المحاسبية",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/company/accounting-app",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Office/Business :: Financial :: Accounting",
        "Topic :: Office/Business :: Financial :: Point-Of-Sale",
        "Natural Language :: Arabic",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.4.3",
            "pytest-asyncio>=0.21.1",
            "black>=23.11.0",
            "flake8>=6.1.0",
            "mypy>=1.7.0",
        ],
        "windows": [
            "pywin32>=306",
        ],
    },
    entry_points={
        "console_scripts": [
            "accounting-app=main:main",
            "accounting-server=run_server:main",
            "accounting-gui=run_gui:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": [
            "config/*.json",
            "config/*.env*",
            "config/*.md",
            "static/*",
            "templates/*",
            "*.md",
            "*.txt",
        ],
    },
    data_files=[
        ("config", [
            "config/.env.example",
            "config/google_credentials_example.json",
            "config/GOOGLE_DRIVE_SETUP.md",
        ]),
        ("docs", [
            "README.md",
        ]),
    ],
    zip_safe=False,
    keywords="accounting, subscribers, management, arabic, desktop, web",
    project_urls={
        "Bug Reports": "https://github.com/company/accounting-app/issues",
        "Source": "https://github.com/company/accounting-app",
        "Documentation": "https://github.com/company/accounting-app/wiki",
    },
)
