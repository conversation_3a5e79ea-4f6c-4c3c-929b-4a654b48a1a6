"""
نموذج المعاملات المالية
"""
from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.database import Base


class Transaction(Base):
    """نموذج المعاملة المالية"""
    __tablename__ = "transactions"
    
    id = Column(Integer, primary_key=True, index=True)
    transaction_number = Column(String(20), unique=True, nullable=False, index=True)
    
    # نوع المعاملة
    type = Column(String(20), nullable=False)  # subscription, router_sale, renewal, refund, expense, salary
    category = Column(String(50), nullable=True)  # تصنيف فرعي
    
    # المبلغ والعملة
    amount = Column(Float, nullable=False)
    currency = Column(String(3), default="USD")
    
    # طريقة الدفع
    payment_method = Column(String(20), nullable=False)  # cash, card, bank_transfer, check
    payment_reference = Column(String(50), nullable=True)  # رقم مرجعي للدفع
    
    # معلومات المعاملة
    description = Column(Text, nullable=True)
    notes = Column(Text, nullable=True)
    
    # الحالة
    status = Column(String(20), nullable=False, default="completed")  # pending, completed, cancelled, refunded
    
    # الربط مع الكيانات الأخرى
    subscriber_id = Column(Integer, ForeignKey("subscribers.id"), nullable=True)
    router_id = Column(Integer, ForeignKey("routers.id"), nullable=True)
    package_id = Column(Integer, ForeignKey("packages.id"), nullable=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)  # المستخدم الذي أجرى المعاملة
    
    # معلومات الصندوق
    cash_box_id = Column(Integer, ForeignKey("cash_boxes.id"), nullable=True)
    
    # تواريخ النظام
    transaction_date = Column(DateTime(timezone=True), server_default=func.now())
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # العلاقات
    subscriber = relationship("Subscriber", back_populates="transactions")
    router = relationship("Router")
    package = relationship("Package")
    user = relationship("User")
    cash_box = relationship("CashBox", back_populates="transactions")
    
    def __repr__(self):
        return f"<Transaction(number='{self.transaction_number}', type='{self.type}', amount={self.amount})>"
    
    @property
    def is_income(self):
        """التحقق من كون المعاملة دخل"""
        income_types = ["subscription", "router_sale", "renewal", "service_fee"]
        return self.type in income_types
    
    @property
    def is_expense(self):
        """التحقق من كون المعاملة مصروف"""
        expense_types = ["expense", "salary", "purchase", "refund"]
        return self.type in expense_types
    
    def generate_receipt_data(self):
        """إنشاء بيانات الإيصال"""
        return {
            "transaction_number": self.transaction_number,
            "date": self.transaction_date,
            "type": self.type,
            "amount": self.amount,
            "payment_method": self.payment_method,
            "description": self.description,
            "subscriber": self.subscriber.name if self.subscriber else None,
            "user": self.user.full_name
        }


class CashBox(Base):
    """نموذج الصندوق اليومي"""
    __tablename__ = "cash_boxes"
    
    id = Column(Integer, primary_key=True, index=True)
    date = Column(DateTime(timezone=True), nullable=False, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # أرصدة البداية
    opening_cash = Column(Float, default=0.0)
    opening_bank = Column(Float, default=0.0)
    
    # أرصدة النهاية
    closing_cash = Column(Float, default=0.0)
    closing_bank = Column(Float, default=0.0)
    
    # الحالة
    is_closed = Column(Boolean, default=False)
    closed_at = Column(DateTime(timezone=True), nullable=True)
    
    # ملاحظات
    notes = Column(Text, nullable=True)
    
    # تواريخ النظام
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # العلاقات
    user = relationship("User")
    transactions = relationship("Transaction", back_populates="cash_box")
    
    def __repr__(self):
        return f"<CashBox(date='{self.date}', user='{self.user.username}', closed={self.is_closed})>"
    
    @property
    def total_income(self):
        """إجمالي الدخل"""
        return sum(t.amount for t in self.transactions if t.is_income)
    
    @property
    def total_expenses(self):
        """إجمالي المصروفات"""
        return sum(t.amount for t in self.transactions if t.is_expense)
    
    @property
    def net_amount(self):
        """صافي المبلغ"""
        return self.total_income - self.total_expenses
