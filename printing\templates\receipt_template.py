"""
قوالب إيصالات الطباعة
"""
from datetime import datetime
from typing import Dict, Any, List


class ReceiptTemplate:
    """قالب الإيصال الأساسي"""
    
    def __init__(self, company_info: Dict[str, str]):
        self.company_info = company_info
    
    def format_subscription_receipt(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """تنسيق إيصال اشتراك"""
        return {
            'number': data.get('transaction_number', 'N/A'),
            'date': data.get('date', datetime.now().strftime('%Y-%m-%d')),
            'customer_name': data.get('subscriber_name', 'N/A'),
            'payment_method': self._format_payment_method(data.get('payment_method', 'cash')),
            'items': [
                {
                    'description': f"اشتراك {data.get('package_name', 'غير محدد')}",
                    'quantity': 1,
                    'price': data.get('amount', 0),
                    'amount': data.get('amount', 0)
                }
            ],
            'subtotal': data.get('amount', 0),
            'tax': 0,
            'total': data.get('amount', 0),
            'notes': data.get('notes', '')
        }
    
    def format_router_receipt(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """تنسيق إيصال تسليم راوتر"""
        return {
            'number': data.get('transaction_number', 'N/A'),
            'date': data.get('date', datetime.now().strftime('%Y-%m-%d')),
            'customer_name': data.get('subscriber_name', 'N/A'),
            'payment_method': self._format_payment_method(data.get('payment_method', 'cash')),
            'items': [
                {
                    'description': f"راوتر {data.get('router_model', 'غير محدد')} - {data.get('router_serial', 'N/A')}",
                    'quantity': 1,
                    'price': data.get('amount', 0),
                    'amount': data.get('amount', 0)
                }
            ],
            'subtotal': data.get('amount', 0),
            'tax': 0,
            'total': data.get('amount', 0),
            'notes': data.get('notes', '')
        }
    
    def format_renewal_receipt(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """تنسيق إيصال تجديد"""
        return {
            'number': data.get('transaction_number', 'N/A'),
            'date': data.get('date', datetime.now().strftime('%Y-%m-%d')),
            'customer_name': data.get('subscriber_name', 'N/A'),
            'payment_method': self._format_payment_method(data.get('payment_method', 'cash')),
            'items': [
                {
                    'description': f"تجديد {data.get('package_name', 'غير محدد')} - {data.get('duration', 30)} يوم",
                    'quantity': 1,
                    'price': data.get('amount', 0),
                    'amount': data.get('amount', 0)
                }
            ],
            'subtotal': data.get('amount', 0),
            'tax': 0,
            'total': data.get('amount', 0),
            'notes': data.get('notes', ''),
            'renewal_info': {
                'old_end_date': data.get('old_end_date', ''),
                'new_end_date': data.get('new_end_date', '')
            }
        }
    
    def format_payment_receipt(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """تنسيق إيصال دفع عام"""
        return {
            'number': data.get('transaction_number', 'N/A'),
            'date': data.get('date', datetime.now().strftime('%Y-%m-%d')),
            'customer_name': data.get('customer_name', 'N/A'),
            'payment_method': self._format_payment_method(data.get('payment_method', 'cash')),
            'items': [
                {
                    'description': data.get('description', 'دفعة'),
                    'quantity': 1,
                    'price': data.get('amount', 0),
                    'amount': data.get('amount', 0)
                }
            ],
            'subtotal': data.get('amount', 0),
            'tax': 0,
            'total': data.get('amount', 0),
            'notes': data.get('notes', '')
        }
    
    def _format_payment_method(self, method: str) -> str:
        """تنسيق طريقة الدفع"""
        methods = {
            'cash': 'نقدي',
            'card': 'بطاقة ائتمان',
            'bank_transfer': 'تحويل بنكي',
            'check': 'شيك'
        }
        return methods.get(method, method)


class ReportTemplate:
    """قالب التقارير"""
    
    def __init__(self, company_info: Dict[str, str]):
        self.company_info = company_info
    
    def format_daily_report(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """تنسيق التقرير اليومي"""
        return {
            'title': f"التقرير اليومي - {data.get('date', datetime.now().strftime('%Y-%m-%d'))}",
            'period': data.get('date', datetime.now().strftime('%Y-%m-%d')),
            'content': self._build_daily_content(data),
            'tables': [
                {
                    'title': 'ملخص المبيعات',
                    'data': [
                        ['النوع', 'العدد', 'المبلغ'],
                        ['اشتراكات جديدة', str(data.get('total_subscriptions', 0)), f"${data.get('subscription_amount', 0):.2f}"],
                        ['تجديدات', str(data.get('total_renewals', 0)), f"${data.get('renewal_amount', 0):.2f}"],
                        ['مبيعات راوترات', str(data.get('total_router_sales', 0)), f"${data.get('router_sales_amount', 0):.2f}"],
                        ['الإجمالي', str(data.get('total_transactions', 0)), f"${data.get('total_sales', 0):.2f}"]
                    ]
                },
                {
                    'title': 'إحصائيات المشتركين',
                    'data': [
                        ['البيان', 'العدد'],
                        ['مشتركين جدد', str(data.get('new_subscribers', 0))],
                        ['مشتركين نشطين', str(data.get('active_subscribers', 0))],
                        ['اشتراكات منتهية', str(data.get('expired_subscriptions', 0))]
                    ]
                }
            ]
        }
    
    def format_monthly_report(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """تنسيق التقرير الشهري"""
        return {
            'title': f"التقرير الشهري - {data.get('year', datetime.now().year)}/{data.get('month', datetime.now().month)}",
            'period': f"{data.get('year', datetime.now().year)}/{data.get('month', datetime.now().month)}",
            'content': self._build_monthly_content(data),
            'tables': [
                {
                    'title': 'الملخص المالي',
                    'data': [
                        ['البيان', 'المبلغ'],
                        ['إجمالي الإيرادات', f"${data.get('total_revenue', 0):.2f}"],
                        ['إجمالي المصروفات', f"${data.get('total_expenses', 0):.2f}"],
                        ['صافي الربح', f"${data.get('net_profit', 0):.2f}"],
                        ['هامش الربح', f"{data.get('profit_margin', 0):.1f}%"]
                    ]
                },
                {
                    'title': 'تفصيل الإيرادات',
                    'data': [
                        ['المصدر', 'المبلغ'],
                        ['إيرادات الاشتراكات', f"${data.get('subscription_revenue', 0):.2f}"],
                        ['مبيعات الراوترات', f"${data.get('router_sales_revenue', 0):.2f}"],
                        ['إيرادات الخدمات', f"${data.get('service_revenue', 0):.2f}"]
                    ]
                }
            ]
        }
    
    def format_financial_summary(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """تنسيق الملخص المالي"""
        period = data.get('period', {})
        summary = data.get('summary', {})
        
        return {
            'title': 'الملخص المالي',
            'period': f"من {period.get('start_date', '')} إلى {period.get('end_date', '')}",
            'content': self._build_financial_summary_content(data),
            'tables': [
                {
                    'title': 'الملخص العام',
                    'data': [
                        ['البيان', 'المبلغ'],
                        ['إجمالي الدخل', f"${summary.get('total_income', 0):.2f}"],
                        ['إجمالي المصروفات', f"${summary.get('total_expenses', 0):.2f}"],
                        ['صافي الربح', f"${summary.get('net_profit', 0):.2f}"],
                        ['هامش الربح', f"{summary.get('profit_margin', 0):.1f}%"],
                        ['عدد المعاملات', str(summary.get('transactions_count', 0))]
                    ]
                },
                {
                    'title': 'تفصيل الدخل حسب النوع',
                    'data': [['النوع', 'المبلغ']] + [
                        [income_type, f"${amount:.2f}"]
                        for income_type, amount in data.get('income_breakdown', {}).items()
                    ]
                },
                {
                    'title': 'تفصيل المصروفات حسب النوع',
                    'data': [['النوع', 'المبلغ']] + [
                        [expense_type, f"${amount:.2f}"]
                        for expense_type, amount in data.get('expenses_breakdown', {}).items()
                    ]
                }
            ]
        }
    
    def _build_daily_content(self, data: Dict[str, Any]) -> str:
        """بناء محتوى التقرير اليومي"""
        content = f"""
        <b>التقرير اليومي ليوم {data.get('date', datetime.now().strftime('%Y-%m-%d'))}</b><br/><br/>
        
        <b>ملخص العمليات:</b><br/>
        • إجمالي المبيعات: ${data.get('total_sales', 0):.2f}<br/>
        • عدد المعاملات: {data.get('total_transactions', 0)}<br/>
        • مشتركين جدد: {data.get('new_subscribers', 0)}<br/>
        • راوترات مسلمة: {data.get('routers_delivered', 0)}<br/><br/>
        
        <b>الأداء المالي:</b><br/>
        • الدخل النقدي: ${data.get('cash_income', 0):.2f}<br/>
        • الدخل بالبطاقة: ${data.get('card_income', 0):.2f}<br/>
        • إجمالي المصروفات: ${data.get('total_expenses', 0):.2f}<br/>
        • صافي الدخل: ${data.get('net_income', 0):.2f}<br/>
        """
        return content
    
    def _build_monthly_content(self, data: Dict[str, Any]) -> str:
        """بناء محتوى التقرير الشهري"""
        content = f"""
        <b>التقرير الشهري لشهر {data.get('month', datetime.now().month)}/{data.get('year', datetime.now().year)}</b><br/><br/>
        
        <b>الأداء المالي:</b><br/>
        • إجمالي الإيرادات: ${data.get('total_revenue', 0):.2f}<br/>
        • إجمالي المصروفات: ${data.get('total_expenses', 0):.2f}<br/>
        • صافي الربح: ${data.get('net_profit', 0):.2f}<br/><br/>
        
        <b>إحصائيات المشتركين:</b><br/>
        • مشتركين جدد: {data.get('new_subscribers_count', 0)}<br/>
        • إجمالي المشتركين: {data.get('total_subscribers', 0)}<br/>
        • معدل الإلغاء: {data.get('churn_rate', 0):.1f}%<br/>
        """
        return content
    
    def _build_financial_summary_content(self, data: Dict[str, Any]) -> str:
        """بناء محتوى الملخص المالي"""
        period = data.get('period', {})
        summary = data.get('summary', {})
        
        content = f"""
        <b>الملخص المالي للفترة من {period.get('start_date', '')} إلى {period.get('end_date', '')}</b><br/><br/>
        
        <b>نظرة عامة:</b><br/>
        • عدد الأيام: {period.get('days', 0)}<br/>
        • عدد المعاملات: {summary.get('transactions_count', 0)}<br/>
        • متوسط المعاملات اليومية: {summary.get('transactions_count', 0) / max(period.get('days', 1), 1):.1f}<br/><br/>
        
        <b>الأداء المالي:</b><br/>
        • إجمالي الدخل: ${summary.get('total_income', 0):.2f}<br/>
        • إجمالي المصروفات: ${summary.get('total_expenses', 0):.2f}<br/>
        • صافي الربح: ${summary.get('net_profit', 0):.2f}<br/>
        • هامش الربح: {summary.get('profit_margin', 0):.1f}%<br/>
        """
        return content
