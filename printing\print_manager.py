"""
مدير الطباعة الرئيسي
"""
import os
import platform
from typing import Dict, Any, Optional
from datetime import datetime
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.units import mm
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib import colors

from app.core.config import settings


class PrintManager:
    """مدير الطباعة الرئيسي"""
    
    def __init__(self):
        self.printer_type = settings.default_printer_type
        self.company_info = {
            "name": settings.company_name,
            "phone": settings.company_phone,
            "address": settings.company_address,
            "logo_path": settings.company_logo_path
        }
        self.setup_fonts()
    
    def setup_fonts(self):
        """إعداد الخطوط العربية"""
        try:
            # محاولة تحميل خط عربي
            font_paths = [
                "C:/Windows/Fonts/arial.ttf",  # Windows
                "/System/Library/Fonts/Arial.ttf",  # macOS
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf"  # Linux
            ]
            
            for font_path in font_paths:
                if os.path.exists(font_path):
                    pdfmetrics.registerFont(TTFont('Arabic', font_path))
                    break
            else:
                # استخدام خط افتراضي
                print("تحذير: لم يتم العثور على خط عربي، سيتم استخدام الخط الافتراضي")
        except Exception as e:
            print(f"خطأ في تحميل الخطوط: {e}")
    
    def print_receipt(self, receipt_data: Dict[str, Any]) -> bool:
        """طباعة إيصال"""
        try:
            if self.printer_type == "thermal_80mm":
                return self.print_thermal_receipt(receipt_data)
            elif self.printer_type == "a4":
                return self.print_a4_receipt(receipt_data)
            else:
                return self.save_receipt_as_pdf(receipt_data)
        except Exception as e:
            print(f"خطأ في الطباعة: {e}")
            return False
    
    def print_thermal_receipt(self, receipt_data: Dict[str, Any]) -> bool:
        """طباعة إيصال حراري 80mm"""
        try:
            # إنشاء ملف PDF مؤقت للطباعة الحرارية
            filename = f"temp_receipt_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            filepath = os.path.join(settings.pdf_output_path, filename)
            
            # إنشاء PDF بحجم 80mm
            width = 80 * mm
            height = 200 * mm  # ارتفاع متغير
            
            c = canvas.Canvas(filepath, pagesize=(width, height))
            
            # إعدادات النص
            y_position = height - 20
            line_height = 12
            
            # رأس الإيصال
            c.setFont("Helvetica-Bold", 12)
            c.drawCentredText(width/2, y_position, self.company_info["name"])
            y_position -= line_height
            
            c.setFont("Helvetica", 10)
            c.drawCentredText(width/2, y_position, self.company_info["phone"])
            y_position -= line_height
            
            c.drawCentredText(width/2, y_position, self.company_info["address"])
            y_position -= line_height * 2
            
            # خط فاصل
            c.line(10, y_position, width-10, y_position)
            y_position -= line_height
            
            # معلومات الإيصال
            c.setFont("Helvetica-Bold", 10)
            c.drawString(10, y_position, f"Receipt No: {receipt_data.get('number', 'N/A')}")
            y_position -= line_height
            
            c.drawString(10, y_position, f"Date: {receipt_data.get('date', datetime.now().strftime('%Y-%m-%d'))}")
            y_position -= line_height
            
            c.drawString(10, y_position, f"Customer: {receipt_data.get('customer_name', 'N/A')}")
            y_position -= line_height * 2
            
            # تفاصيل العناصر
            c.setFont("Helvetica", 9)
            for item in receipt_data.get('items', []):
                item_text = f"{item.get('description', '')} - ${item.get('amount', 0):.2f}"
                c.drawString(10, y_position, item_text)
                y_position -= line_height
            
            y_position -= line_height
            
            # خط فاصل
            c.line(10, y_position, width-10, y_position)
            y_position -= line_height
            
            # الإجمالي
            c.setFont("Helvetica-Bold", 12)
            total_text = f"Total: ${receipt_data.get('total', 0):.2f}"
            c.drawString(10, y_position, total_text)
            y_position -= line_height * 2
            
            # رسالة شكر
            c.setFont("Helvetica", 8)
            c.drawCentredText(width/2, y_position, "Thank you for your business!")
            
            c.save()
            
            # طباعة الملف
            return self.send_to_printer(filepath)
            
        except Exception as e:
            print(f"خطأ في طباعة الإيصال الحراري: {e}")
            return False
    
    def print_a4_receipt(self, receipt_data: Dict[str, Any]) -> bool:
        """طباعة إيصال A4"""
        try:
            filename = f"receipt_a4_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            filepath = os.path.join(settings.pdf_output_path, filename)
            
            doc = SimpleDocTemplate(filepath, pagesize=A4)
            story = []
            styles = getSampleStyleSheet()
            
            # رأس الإيصال
            title_style = styles['Title']
            title_style.alignment = 1  # وسط
            story.append(Paragraph(self.company_info["name"], title_style))
            story.append(Spacer(1, 12))
            
            # معلومات الشركة
            company_info = f"""
            <para align=center>
            {self.company_info["phone"]}<br/>
            {self.company_info["address"]}
            </para>
            """
            story.append(Paragraph(company_info, styles['Normal']))
            story.append(Spacer(1, 20))
            
            # معلومات الإيصال
            receipt_info = [
                ['Receipt Number:', receipt_data.get('number', 'N/A')],
                ['Date:', receipt_data.get('date', datetime.now().strftime('%Y-%m-%d'))],
                ['Customer:', receipt_data.get('customer_name', 'N/A')],
                ['Payment Method:', receipt_data.get('payment_method', 'Cash')]
            ]
            
            info_table = Table(receipt_info, colWidths=[100, 200])
            info_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ]))
            
            story.append(info_table)
            story.append(Spacer(1, 20))
            
            # جدول العناصر
            items_data = [['Description', 'Quantity', 'Price', 'Total']]
            
            for item in receipt_data.get('items', []):
                items_data.append([
                    item.get('description', ''),
                    str(item.get('quantity', 1)),
                    f"${item.get('price', 0):.2f}",
                    f"${item.get('amount', 0):.2f}"
                ])
            
            items_table = Table(items_data, colWidths=[200, 60, 80, 80])
            items_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(items_table)
            story.append(Spacer(1, 20))
            
            # الإجمالي
            total_data = [
                ['Subtotal:', f"${receipt_data.get('subtotal', 0):.2f}"],
                ['Tax:', f"${receipt_data.get('tax', 0):.2f}"],
                ['Total:', f"${receipt_data.get('total', 0):.2f}"]
            ]
            
            total_table = Table(total_data, colWidths=[300, 100])
            total_table.setStyle(TableStyle([
                ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
                ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 12),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
                ('LINEABOVE', (0, -1), (-1, -1), 2, colors.black),
            ]))
            
            story.append(total_table)
            story.append(Spacer(1, 30))
            
            # رسالة شكر
            thank_you = "<para align=center><b>Thank you for your business!</b></para>"
            story.append(Paragraph(thank_you, styles['Normal']))
            
            doc.build(story)
            
            # طباعة الملف
            return self.send_to_printer(filepath)
            
        except Exception as e:
            print(f"خطأ في طباعة إيصال A4: {e}")
            return False
    
    def save_receipt_as_pdf(self, receipt_data: Dict[str, Any]) -> bool:
        """حفظ الإيصال كـ PDF"""
        try:
            filename = f"receipt_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            filepath = os.path.join(settings.pdf_output_path, filename)
            
            # استخدام نفس منطق A4 ولكن بدون طباعة
            return self.print_a4_receipt(receipt_data)
            
        except Exception as e:
            print(f"خطأ في حفظ PDF: {e}")
            return False
    
    def send_to_printer(self, filepath: str) -> bool:
        """إرسال الملف للطابعة"""
        try:
            system = platform.system()
            
            if system == "Windows":
                # Windows
                os.startfile(filepath, "print")
            elif system == "Darwin":
                # macOS
                os.system(f"lpr '{filepath}'")
            else:
                # Linux
                os.system(f"lp '{filepath}'")
            
            return True
            
        except Exception as e:
            print(f"خطأ في إرسال الملف للطابعة: {e}")
            return False
    
    def print_report(self, report_data: Dict[str, Any], report_type: str = "general") -> bool:
        """طباعة تقرير"""
        try:
            filename = f"report_{report_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            filepath = os.path.join(settings.pdf_output_path, filename)
            
            doc = SimpleDocTemplate(filepath, pagesize=A4)
            story = []
            styles = getSampleStyleSheet()
            
            # رأس التقرير
            title = report_data.get('title', 'تقرير')
            story.append(Paragraph(title, styles['Title']))
            story.append(Spacer(1, 20))
            
            # معلومات التقرير
            report_info = f"""
            <para align=center>
            {self.company_info["name"]}<br/>
            تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}<br/>
            الفترة: {report_data.get('period', 'غير محدد')}
            </para>
            """
            story.append(Paragraph(report_info, styles['Normal']))
            story.append(Spacer(1, 30))
            
            # محتوى التقرير
            if 'content' in report_data:
                story.append(Paragraph(report_data['content'], styles['Normal']))
            
            # جداول البيانات
            if 'tables' in report_data:
                for table_data in report_data['tables']:
                    if 'title' in table_data:
                        story.append(Paragraph(table_data['title'], styles['Heading2']))
                        story.append(Spacer(1, 10))
                    
                    table = Table(table_data['data'])
                    table.setStyle(TableStyle([
                        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                        ('FONTSIZE', (0, 0), (-1, -1), 9),
                        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                        ('GRID', (0, 0), (-1, -1), 1, colors.black)
                    ]))
                    
                    story.append(table)
                    story.append(Spacer(1, 20))
            
            doc.build(story)
            
            # طباعة التقرير
            return self.send_to_printer(filepath)
            
        except Exception as e:
            print(f"خطأ في طباعة التقرير: {e}")
            return False
    
    def get_available_printers(self) -> list:
        """الحصول على قائمة الطابعات المتاحة"""
        try:
            system = platform.system()
            printers = []
            
            if system == "Windows":
                import win32print
                printers = [printer[2] for printer in win32print.EnumPrinters(2)]
            elif system == "Darwin":
                # macOS
                import subprocess
                result = subprocess.run(['lpstat', '-p'], capture_output=True, text=True)
                for line in result.stdout.split('\n'):
                    if line.startswith('printer'):
                        printers.append(line.split()[1])
            else:
                # Linux
                import subprocess
                result = subprocess.run(['lpstat', '-p'], capture_output=True, text=True)
                for line in result.stdout.split('\n'):
                    if line.startswith('printer'):
                        printers.append(line.split()[1])
            
            return printers
            
        except Exception as e:
            print(f"خطأ في الحصول على قائمة الطابعات: {e}")
            return []
    
    def test_printer(self, printer_name: Optional[str] = None) -> bool:
        """اختبار الطابعة"""
        try:
            test_data = {
                'number': 'TEST-001',
                'date': datetime.now().strftime('%Y-%m-%d'),
                'customer_name': 'Test Customer',
                'items': [
                    {'description': 'Test Item', 'quantity': 1, 'price': 10.00, 'amount': 10.00}
                ],
                'total': 10.00
            }
            
            return self.print_receipt(test_data)
            
        except Exception as e:
            print(f"خطأ في اختبار الطابعة: {e}")
            return False
