"""
إعداد قاعدة البيانات والاتصال
"""
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
from app.core.config import settings

# إنشاء محرك قاعدة البيانات
engine = create_engine(
    settings.database_url,
    connect_args={
        "check_same_thread": False,  # للـ SQLite فقط
        "timeout": 20
    },
    poolclass=StaticPool,
    echo=settings.debug
)

# إنشاء جلسة قاعدة البيانات
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# إنشاء قاعدة النماذج
Base = declarative_base()

# Metadata للجداول
metadata = MetaData()


def get_db():
    """الحصول على جلسة قاعدة البيانات"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def init_db():
    """تهيئة قاعدة البيانات وإنشاء الجداول"""
    # استيراد جميع النماذج لضمان إنشاء الجداول
    from app.models import (
        user, subscriber, package, router, 
        transaction, inventory, cable, report
    )
    
    # إنشاء الجداول
    Base.metadata.create_all(bind=engine)
    
    # إنشاء البيانات الأولية
    await create_initial_data()


async def create_initial_data():
    """إنشاء البيانات الأولية"""
    from app.models.user import User
    from app.models.package import Package
    from app.services.auth import get_password_hash
    
    db = SessionLocal()
    try:
        # التحقق من وجود مستخدم مدير
        admin_user = db.query(User).filter(User.username == "admin").first()
        if not admin_user:
            # إنشاء مستخدم مدير افتراضي
            admin_user = User(
                username="admin",
                email="<EMAIL>",
                full_name="المدير العام",
                hashed_password=get_password_hash("admin123"),
                role="admin",
                is_active=True
            )
            db.add(admin_user)
        
        # إنشاء باقات افتراضية
        default_packages = [
            {"name": "باقة 1 ميجا", "speed": 1, "price": 50.0, "type": "monthly"},
            {"name": "باقة 2 ميجا", "speed": 2, "price": 80.0, "type": "monthly"},
            {"name": "باقة 5 ميجا", "speed": 5, "price": 150.0, "type": "monthly"},
            {"name": "باقة 10 ميجا", "speed": 10, "price": 250.0, "type": "monthly"},
        ]
        
        for pkg_data in default_packages:
            existing_pkg = db.query(Package).filter(Package.name == pkg_data["name"]).first()
            if not existing_pkg:
                package = Package(**pkg_data)
                db.add(package)
        
        db.commit()
        
    except Exception as e:
        db.rollback()
        raise e
    finally:
        db.close()
