#!/usr/bin/env python3
"""
ملف تشغيل مبسط للتطبيق
"""
import os
import sys
import subprocess
from pathlib import Path

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    return True

def check_virtual_environment():
    """التحقق من البيئة الافتراضية"""
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ تعمل في بيئة افتراضية")
        return True
    else:
        print("⚠️ تحذير: لا تعمل في بيئة افتراضية")
        print("يُنصح بإنشاء بيئة افتراضية:")
        print("python -m venv venv")
        print("venv\\Scripts\\activate  # Windows")
        print("source venv/bin/activate  # Linux/macOS")
        return False

def install_requirements():
    """تثبيت المتطلبات"""
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("❌ ملف requirements.txt غير موجود")
        return False
    
    try:
        print("📦 تثبيت المتطلبات...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم تثبيت المتطلبات بنجاح")
            return True
        else:
            print("❌ فشل في تثبيت المتطلبات:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ خطأ في تثبيت المتطلبات: {e}")
        return False

def check_config_files():
    """التحقق من ملفات الإعداد"""
    config_dir = Path("config")
    
    # إنشاء مجلد config إذا لم يكن موجوداً
    config_dir.mkdir(exist_ok=True)
    
    # التحقق من ملف .env
    env_file = Path("config/.env")
    env_example = Path("config/.env.example")
    
    if not env_file.exists() and env_example.exists():
        print("📝 إنشاء ملف الإعداد...")
        try:
            import shutil
            shutil.copy(env_example, env_file)
            print("✅ تم إنشاء config/.env من المثال")
            print("⚠️ يرجى مراجعة وتعديل الإعدادات في config/.env")
        except Exception as e:
            print(f"❌ فشل في إنشاء ملف الإعداد: {e}")
            return False
    
    return True

def check_database():
    """التحقق من قاعدة البيانات"""
    data_dir = Path("data")
    data_dir.mkdir(exist_ok=True)
    
    db_file = data_dir / "accounting.db"
    if db_file.exists():
        print("✅ قاعدة البيانات موجودة")
    else:
        print("ℹ️ سيتم إنشاء قاعدة البيانات عند أول تشغيل")
    
    return True

def run_application():
    """تشغيل التطبيق"""
    try:
        print("🚀 تشغيل التطبيق...")
        
        # إضافة المسار الحالي إلى PYTHONPATH
        current_dir = str(Path.cwd())
        env = os.environ.copy()
        if 'PYTHONPATH' in env:
            env['PYTHONPATH'] = f"{current_dir}{os.pathsep}{env['PYTHONPATH']}"
        else:
            env['PYTHONPATH'] = current_dir
        
        # تشغيل التطبيق
        result = subprocess.run([sys.executable, "main.py"], env=env)
        return result.returncode == 0
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
        return True
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🏢 تطبيق المحاسبة المتكامل")
    print("=" * 50)
    
    # التحقق من المتطلبات
    if not check_python_version():
        return 1
    
    check_virtual_environment()
    
    if not check_config_files():
        return 1
    
    if not check_database():
        return 1
    
    # محاولة تثبيت المتطلبات
    try:
        import fastapi
        import PyQt6
        print("✅ المتطلبات الأساسية متوفرة")
    except ImportError:
        print("📦 بعض المتطلبات غير متوفرة، محاولة التثبيت...")
        if not install_requirements():
            print("❌ فشل في تثبيت المتطلبات")
            print("يرجى تثبيت المتطلبات يدوياً:")
            print("pip install -r requirements.txt")
            return 1
    
    # تشغيل التطبيق
    success = run_application()
    
    if success:
        print("✅ تم إنهاء التطبيق بنجاح")
        return 0
    else:
        print("❌ حدث خطأ أثناء تشغيل التطبيق")
        return 1

if __name__ == "__main__":
    sys.exit(main())
