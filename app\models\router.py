"""
نموذج الراوترات
"""
from sqlalchemy import Column, Integer, String, Float, Boolean, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.db.database import Base


class Router(Base):
    """نموذج الراوتر"""
    __tablename__ = "routers"
    
    id = Column(Integer, primary_key=True, index=True)
    serial_number = Column(String(50), unique=True, nullable=False, index=True)
    model = Column(String(50), nullable=False)
    brand = Column(String(50), nullable=False)
    
    # معلومات الشراء
    purchase_price = Column(Float, nullable=False)
    purchase_date = Column(DateTime(timezone=True), nullable=True)
    supplier = Column(String(100), nullable=True)
    
    # حالة الراوتر
    status = Column(String(20), nullable=False, default="available")  # available, assigned, damaged, returned
    condition = Column(String(20), nullable=False, default="new")  # new, used, refurbished, damaged
    
    # معلومات التسليم
    assigned_to = Column(Integer, ForeignKey("subscribers.id"), nullable=True)
    assigned_date = Column(DateTime(timezone=True), nullable=True)
    assigned_by = Column(Integer, ForeignKey("users.id"), nullable=True)
    
    # معلومات الإرجاع
    returned_date = Column(DateTime(timezone=True), nullable=True)
    return_reason = Column(Text, nullable=True)
    return_condition = Column(String(20), nullable=True)
    
    # معلومات إضافية
    mac_address = Column(String(17), nullable=True)  # MAC Address
    firmware_version = Column(String(20), nullable=True)
    notes = Column(Text, nullable=True)
    warranty_end = Column(DateTime(timezone=True), nullable=True)
    
    # تواريخ النظام
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # العلاقات
    assigner = relationship("User")
    
    def __repr__(self):
        return f"<Router(serial='{self.serial_number}', model='{self.model}', status='{self.status}')>"
    
    @property
    def is_available(self):
        """التحقق من توفر الراوتر"""
        return self.status == "available"
    
    @property
    def is_assigned(self):
        """التحقق من تسليم الراوتر"""
        return self.status == "assigned" and self.assigned_to is not None
    
    @property
    def days_in_use(self):
        """عدد الأيام في الاستخدام"""
        if not self.assigned_date:
            return 0
        if self.returned_date:
            return (self.returned_date - self.assigned_date).days
        return (func.now() - self.assigned_date).days
    
    def assign_to_subscriber(self, subscriber_id: int, user_id: int):
        """تسليم الراوتر لمشترك"""
        self.assigned_to = subscriber_id
        self.assigned_date = func.now()
        self.assigned_by = user_id
        self.status = "assigned"
    
    def return_router(self, reason: str = None, condition: str = "used"):
        """إرجاع الراوتر"""
        self.returned_date = func.now()
        self.return_reason = reason
        self.return_condition = condition
        self.status = "available" if condition != "damaged" else "damaged"
        self.assigned_to = None
