"""
مسارات إدارة الباقات
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel

from app.db.database import get_db
from app.services.auth import get_current_active_user, require_permission
from app.models.user import User
from app.models.package import Package

router = APIRouter()


class PackageCreate(BaseModel):
    """نموذج إنشاء باقة"""
    name: str
    description: Optional[str] = None
    speed: int
    price: float
    type: str = "monthly"
    duration_days: int = 30
    data_limit: Optional[int] = None
    is_unlimited: bool = True
    cost_price: float = 0.0
    profit_margin: float = 0.0


class PackageUpdate(BaseModel):
    """نموذج تحديث باقة"""
    name: Optional[str] = None
    description: Optional[str] = None
    speed: Optional[int] = None
    price: Optional[float] = None
    type: Optional[str] = None
    duration_days: Optional[int] = None
    data_limit: Optional[int] = None
    is_unlimited: Optional[bool] = None
    is_active: Optional[bool] = None
    cost_price: Optional[float] = None
    profit_margin: Optional[float] = None


class PackageResponse(BaseModel):
    """استجابة الباقة"""
    id: int
    name: str
    description: Optional[str]
    speed: int
    price: float
    type: str
    duration_days: int
    data_limit: Optional[int]
    is_unlimited: bool
    is_active: bool
    cost_price: float
    profit_margin: float
    created_at: str
    
    # حقول محسوبة
    profit: float
    profit_percentage: float
    subscribers_count: int = 0

    class Config:
        from_attributes = True


@router.get("/", response_model=List[PackageResponse])
async def get_packages(
    skip: int = 0,
    limit: int = 100,
    is_active: Optional[bool] = None,
    current_user: User = Depends(require_permission("packages")),
    db: Session = Depends(get_db)
):
    """الحصول على قائمة الباقات"""
    query = db.query(Package)
    
    if is_active is not None:
        query = query.filter(Package.is_active == is_active)
    
    packages = query.offset(skip).limit(limit).all()
    
    # إضافة معلومات إضافية
    result = []
    for package in packages:
        package_data = PackageResponse.from_orm(package)
        package_data.profit = package.profit
        package_data.profit_percentage = package.profit_percentage
        package_data.subscribers_count = len(package.subscribers)
        result.append(package_data)
    
    return result


@router.get("/{package_id}", response_model=PackageResponse)
async def get_package(
    package_id: int,
    current_user: User = Depends(require_permission("packages")),
    db: Session = Depends(get_db)
):
    """الحصول على باقة محددة"""
    package = db.query(Package).filter(Package.id == package_id).first()
    if not package:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الباقة غير موجودة"
        )
    
    package_data = PackageResponse.from_orm(package)
    package_data.profit = package.profit
    package_data.profit_percentage = package.profit_percentage
    package_data.subscribers_count = len(package.subscribers)
    
    return package_data


@router.post("/", response_model=PackageResponse)
async def create_package(
    package_data: PackageCreate,
    current_user: User = Depends(require_permission("packages")),
    db: Session = Depends(get_db)
):
    """إنشاء باقة جديدة"""
    # التحقق من عدم وجود اسم الباقة
    existing_package = db.query(Package).filter(Package.name == package_data.name).first()
    if existing_package:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="اسم الباقة موجود بالفعل"
        )
    
    # إنشاء الباقة
    package = Package(**package_data.dict())
    
    db.add(package)
    db.commit()
    db.refresh(package)
    
    package_response = PackageResponse.from_orm(package)
    package_response.profit = package.profit
    package_response.profit_percentage = package.profit_percentage
    package_response.subscribers_count = 0
    
    return package_response


@router.put("/{package_id}", response_model=PackageResponse)
async def update_package(
    package_id: int,
    package_data: PackageUpdate,
    current_user: User = Depends(require_permission("packages")),
    db: Session = Depends(get_db)
):
    """تحديث باقة"""
    package = db.query(Package).filter(Package.id == package_id).first()
    if not package:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الباقة غير موجودة"
        )
    
    # تحديث البيانات
    update_data = package_data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(package, field, value)
    
    db.commit()
    db.refresh(package)
    
    package_response = PackageResponse.from_orm(package)
    package_response.profit = package.profit
    package_response.profit_percentage = package.profit_percentage
    package_response.subscribers_count = len(package.subscribers)
    
    return package_response


@router.delete("/{package_id}")
async def delete_package(
    package_id: int,
    current_user: User = Depends(require_permission("packages")),
    db: Session = Depends(get_db)
):
    """حذف باقة"""
    package = db.query(Package).filter(Package.id == package_id).first()
    if not package:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الباقة غير موجودة"
        )
    
    # التحقق من عدم وجود مشتركين مرتبطين
    if package.subscribers:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="لا يمكن حذف الباقة لوجود مشتركين مرتبطين بها"
        )
    
    db.delete(package)
    db.commit()
    
    return {"message": "تم حذف الباقة بنجاح"}


@router.get("/{package_id}/subscribers")
async def get_package_subscribers(
    package_id: int,
    current_user: User = Depends(require_permission("packages")),
    db: Session = Depends(get_db)
):
    """الحصول على مشتركي الباقة"""
    package = db.query(Package).filter(Package.id == package_id).first()
    if not package:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الباقة غير موجودة"
        )
    
    subscribers = []
    for subscriber in package.subscribers:
        subscribers.append({
            "id": subscriber.id,
            "name": subscriber.name,
            "phone": subscriber.phone,
            "is_active": subscriber.is_active,
            "subscription_end": subscriber.subscription_end,
            "created_at": subscriber.created_at
        })
    
    return {
        "package_id": package_id,
        "package_name": package.name,
        "subscribers_count": len(subscribers),
        "subscribers": subscribers
    }


@router.get("/{package_id}/calculate-price")
async def calculate_package_price(
    package_id: int,
    days: int,
    current_user: User = Depends(require_permission("packages")),
    db: Session = Depends(get_db)
):
    """حساب سعر الباقة لعدد أيام محدد"""
    package = db.query(Package).filter(Package.id == package_id).first()
    if not package:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الباقة غير موجودة"
        )
    
    calculated_price = package.calculate_price_for_days(days)
    
    return {
        "package_id": package_id,
        "package_name": package.name,
        "days": days,
        "calculated_price": calculated_price,
        "base_price": package.price,
        "price_per_day": calculated_price / days if days > 0 else 0
    }
