#!/usr/bin/env python3
"""
اختبار بسيط للواجهة
"""
import sys
import os
from pathlib import Path

# إضافة المسار الحالي
current_dir = str(Path.cwd())
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

print("🚀 اختبار واجهة المستخدم...")

try:
    from PyQt6.QtWidgets import QApplication, QWidget, QVBoxLayout, QLabel, QPushButton, QMessageBox
    from PyQt6.QtCore import Qt
    from PyQt6.QtGui import QFont
    
    print("✅ تم تحميل PyQt6")
    
    class TestWindow(QWidget):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("اختبار واجهة المستخدم")
            self.setGeometry(100, 100, 500, 300)
            
            layout = QVBoxLayout()
            
            # العنوان
            title = QLabel("🏢 تطبيق المحاسبة المتكامل")
            title.setAlignment(Qt.AlignmentFlag.AlignCenter)
            title.setFont(QFont("Arial", 18, QFont.Weight.Bold))
            layout.addWidget(title)
            
            # رسالة
            message = QLabel("الواجهة تعمل بشكل صحيح!")
            message.setAlignment(Qt.AlignmentFlag.AlignCenter)
            message.setFont(QFont("Arial", 14))
            layout.addWidget(message)
            
            # زر اختبار تسجيل الدخول
            login_btn = QPushButton("اختبار تسجيل الدخول")
            login_btn.clicked.connect(self.test_login)
            layout.addWidget(login_btn)
            
            # زر إغلاق
            close_btn = QPushButton("إغلاق")
            close_btn.clicked.connect(self.close)
            layout.addWidget(close_btn)
            
            self.setLayout(layout)
        
        def test_login(self):
            try:
                from gui.windows.login_window import LoginWindow
                
                login_window = LoginWindow()
                login_window.show()
                
                QMessageBox.information(self, "نجح", "تم فتح نافذة تسجيل الدخول!")
                
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"فشل في فتح نافذة تسجيل الدخول:\n{e}")
    
    # إنشاء التطبيق
    app = QApplication(sys.argv)
    app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
    
    # تطبيق الأنماط
    app.setStyleSheet("""
    QWidget {
        font-family: 'Segoe UI', 'Tahoma', 'Arial';
        font-size: 12pt;
        background-color: #f5f5f5;
    }
    
    QLabel {
        color: #2c3e50;
        margin: 10px;
    }
    
    QPushButton {
        background-color: #3498db;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        margin: 5px;
    }
    
    QPushButton:hover {
        background-color: #2980b9;
    }
    """)
    
    # إنشاء النافذة
    window = TestWindow()
    window.show()
    
    print("✅ تم عرض نافذة الاختبار")
    print("🔍 تحقق من ظهور النافذة على الشاشة")
    
    # تشغيل التطبيق
    sys.exit(app.exec())
    
except ImportError as e:
    print(f"❌ خطأ في الاستيراد: {e}")
    print("📦 يرجى تثبيت PyQt6:")
    print("   venv\\Scripts\\pip install PyQt6")
    input("اضغط Enter للخروج...")
    
except Exception as e:
    print(f"❌ خطأ عام: {e}")
    input("اضغط Enter للخروج...")
