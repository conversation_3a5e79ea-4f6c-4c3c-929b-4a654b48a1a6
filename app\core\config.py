"""
إعدادات التطبيق الأساسية
"""
import os
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """إعدادات التطبيق"""
    
    # Database
    database_url: str = Field(default="sqlite:///./data/accounting.db")
    
    # Security
    secret_key: str = Field(default="your-secret-key-change-this-in-production")
    algorithm: str = Field(default="HS256")
    access_token_expire_minutes: int = Field(default=30)
    
    # Google Drive
    google_drive_credentials_file: str = Field(default="config/google_credentials.json")
    google_drive_folder_id: Optional[str] = Field(default=None)
    
    # Application
    app_name: str = Field(default="تطبيق المحاسبة المتكامل")
    app_version: str = Field(default="1.0.0")
    debug: bool = Field(default=True)
    
    # Printing
    default_printer_type: str = Field(default="thermal_80mm")
    thermal_printer_width: int = Field(default=80)
    a4_printer_dpi: int = Field(default=300)
    pdf_output_path: str = Field(default="data/prints/")
    
    # Company
    company_name: str = Field(default="اسم الشركة")
    company_phone: str = Field(default="رقم الهاتف")
    company_address: str = Field(default="عنوان الشركة")
    company_logo_path: str = Field(default="gui/resources/logo.png")
    
    # Network
    api_host: str = Field(default="0.0.0.0")
    api_port: int = Field(default=8000)
    enable_cors: bool = Field(default=True)
    
    # Backup
    auto_backup_enabled: bool = Field(default=True)
    backup_interval_hours: int = Field(default=24)
    local_backup_path: str = Field(default="data/backups/")
    
    # Sync
    auto_sync_enabled: bool = Field(default=True)
    sync_interval_minutes: int = Field(default=30)
    offline_mode: bool = Field(default=False)
    
    class Config:
        env_file = "config/.env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# إنشاء مثيل الإعدادات
settings = Settings()


def get_settings() -> Settings:
    """الحصول على إعدادات التطبيق"""
    return settings


def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = [
        "data",
        "data/backups",
        "data/prints",
        "data/exports",
        "logs"
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)


# إنشاء المجلدات عند استيراد الوحدة
create_directories()
