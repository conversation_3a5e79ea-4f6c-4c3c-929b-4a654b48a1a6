"""
واجهة إدارة المشتركين
"""
import requests
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QLineEdit, QLabel, QMessageBox, QHeaderView, QFrame,
    QComboBox, QDateEdit, QTextEdit, QDialog, QFormLayout, QDialogButtonBox
)
from PyQt6.QtCore import Qt, QDate, pyqtSignal
from PyQt6.QtGui import QFont

from app.core.config import settings


class AddSubscriberDialog(QDialog):
    """نافذة إضافة مشترك جديد"""
    
    def __init__(self, access_token, parent=None):
        super().__init__(parent)
        self.access_token = access_token
        self.init_ui()
    
    def init_ui(self):
        """تهيئة واجهة النافذة"""
        self.setWindowTitle("إضافة مشترك جديد")
        self.setModal(True)
        self.resize(400, 500)
        
        layout = QVBoxLayout(self)
        
        # نموذج البيانات
        form_layout = QFormLayout()
        
        # الاسم
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("أدخل اسم المشترك")
        form_layout.addRow("الاسم:", self.name_input)
        
        # رقم الهاتف
        self.phone_input = QLineEdit()
        self.phone_input.setPlaceholderText("أدخل رقم الهاتف")
        form_layout.addRow("رقم الهاتف:", self.phone_input)
        
        # البريد الإلكتروني
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("أدخل البريد الإلكتروني (اختياري)")
        form_layout.addRow("البريد الإلكتروني:", self.email_input)
        
        # العنوان
        self.address_input = QTextEdit()
        self.address_input.setPlaceholderText("أدخل العنوان")
        self.address_input.setMaximumHeight(80)
        form_layout.addRow("العنوان:", self.address_input)
        
        # الرقم القومي
        self.national_id_input = QLineEdit()
        self.national_id_input.setPlaceholderText("أدخل الرقم القومي (اختياري)")
        form_layout.addRow("الرقم القومي:", self.national_id_input)
        
        # الباقة
        self.package_combo = QComboBox()
        self.load_packages()
        form_layout.addRow("الباقة:", self.package_combo)
        
        # ملاحظات
        self.notes_input = QTextEdit()
        self.notes_input.setPlaceholderText("ملاحظات إضافية (اختياري)")
        self.notes_input.setMaximumHeight(60)
        form_layout.addRow("ملاحظات:", self.notes_input)
        
        layout.addLayout(form_layout)
        
        # أزرار التحكم
        buttons = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
    
    def load_packages(self):
        """تحميل قائمة الباقات"""
        try:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            url = f"http://{settings.api_host}:{settings.api_port}/api/packages/"
            response = requests.get(url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                packages = response.json()
                self.package_combo.addItem("-- اختر الباقة --", None)
                for package in packages:
                    if package["is_active"]:
                        self.package_combo.addItem(
                            f"{package['name']} - {package['price']} USD",
                            package["id"]
                        )
        except Exception as e:
            print(f"خطأ في تحميل الباقات: {e}")
    
    def get_data(self):
        """الحصول على بيانات النموذج"""
        return {
            "name": self.name_input.text().strip(),
            "phone": self.phone_input.text().strip(),
            "email": self.email_input.text().strip() or None,
            "address": self.address_input.toPlainText().strip(),
            "national_id": self.national_id_input.text().strip() or None,
            "package_id": self.package_combo.currentData(),
            "notes": self.notes_input.toPlainText().strip() or None
        }
    
    def validate_data(self):
        """التحقق من صحة البيانات"""
        data = self.get_data()
        
        if not data["name"]:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال اسم المشترك")
            return False
        
        if not data["phone"]:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال رقم الهاتف")
            return False
        
        if not data["address"]:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال العنوان")
            return False
        
        return True
    
    def accept(self):
        """قبول النموذج"""
        if self.validate_data():
            super().accept()


class SubscribersWidget(QWidget):
    """واجهة إدارة المشتركين"""
    
    def __init__(self, user, access_token):
        super().__init__()
        self.user = user
        self.access_token = access_token
        self.subscribers_data = []
        
        self.init_ui()
        self.load_subscribers()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(15)
        
        # شريط الأدوات
        self.create_toolbar(layout)
        
        # شريط البحث والفلاتر
        self.create_search_bar(layout)
        
        # جدول المشتركين
        self.create_subscribers_table(layout)
        
        # شريط الحالة
        self.create_status_bar(layout)
    
    def create_toolbar(self, layout):
        """إنشاء شريط الأدوات"""
        toolbar_frame = QFrame()
        toolbar_layout = QHBoxLayout(toolbar_frame)
        toolbar_layout.setContentsMargins(10, 10, 10, 10)
        
        # زر إضافة مشترك جديد
        add_button = QPushButton("➕ إضافة مشترك جديد")
        add_button.setObjectName("primaryButton")
        add_button.clicked.connect(self.add_subscriber)
        toolbar_layout.addWidget(add_button)
        
        # زر تجديد الاشتراك
        renew_button = QPushButton("🔄 تجديد اشتراك")
        renew_button.setObjectName("secondaryButton")
        renew_button.clicked.connect(self.renew_subscription)
        toolbar_layout.addWidget(renew_button)
        
        # زر تحديث البيانات
        refresh_button = QPushButton("🔄 تحديث")
        refresh_button.clicked.connect(self.load_subscribers)
        toolbar_layout.addWidget(refresh_button)
        
        # مساحة فارغة
        toolbar_layout.addStretch()
        
        # زر طباعة
        print_button = QPushButton("🖨️ طباعة")
        print_button.clicked.connect(self.print_list)
        toolbar_layout.addWidget(print_button)
        
        # تطبيق الأنماط
        toolbar_frame.setStyleSheet("""
        QPushButton#primaryButton {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: bold;
        }
        
        QPushButton#primaryButton:hover {
            background: #45a049;
        }
        
        QPushButton#secondaryButton {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: bold;
        }
        
        QPushButton#secondaryButton:hover {
            background: #1976D2;
        }
        
        QPushButton {
            padding: 8px 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
        }
        
        QPushButton:hover {
            background: #f5f5f5;
        }
        """)
        
        layout.addWidget(toolbar_frame)
    
    def create_search_bar(self, layout):
        """إنشاء شريط البحث"""
        search_frame = QFrame()
        search_layout = QHBoxLayout(search_frame)
        search_layout.setContentsMargins(10, 10, 10, 10)
        
        # حقل البحث
        search_label = QLabel("البحث:")
        search_layout.addWidget(search_label)
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث بالاسم أو رقم الهاتف...")
        self.search_input.textChanged.connect(self.filter_subscribers)
        search_layout.addWidget(self.search_input)
        
        # فلتر الحالة
        status_label = QLabel("الحالة:")
        search_layout.addWidget(status_label)
        
        self.status_combo = QComboBox()
        self.status_combo.addItems(["الكل", "نشط", "غير نشط"])
        self.status_combo.currentTextChanged.connect(self.filter_subscribers)
        search_layout.addWidget(self.status_combo)
        
        # فلتر الباقة
        package_label = QLabel("الباقة:")
        search_layout.addWidget(package_label)
        
        self.package_combo = QComboBox()
        self.package_combo.addItem("الكل", None)
        self.load_packages_filter()
        self.package_combo.currentTextChanged.connect(self.filter_subscribers)
        search_layout.addWidget(self.package_combo)
        
        layout.addWidget(search_frame)
    
    def create_subscribers_table(self, layout):
        """إنشاء جدول المشتركين"""
        self.table = QTableWidget()
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        
        # أعمدة الجدول
        columns = [
            "الرقم", "الاسم", "رقم الهاتف", "الباقة", "تاريخ الانتهاء", 
            "الحالة", "الرصيد", "تاريخ الإنشاء"
        ]
        
        self.table.setColumnCount(len(columns))
        self.table.setHorizontalHeaderLabels(columns)
        
        # تخصيص عرض الأعمدة
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # الاسم
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # الهاتف
        
        # أحداث الجدول
        self.table.doubleClicked.connect(self.edit_subscriber)
        
        layout.addWidget(self.table)
    
    def create_status_bar(self, layout):
        """إنشاء شريط الحالة"""
        self.status_label = QLabel("جاري تحميل البيانات...")
        self.status_label.setStyleSheet("padding: 5px; color: #666;")
        layout.addWidget(self.status_label)
    
    def load_packages_filter(self):
        """تحميل الباقات للفلتر"""
        try:
            headers = {"Authorization": f"Bearer {self.access_token}"}
            url = f"http://{settings.api_host}:{settings.api_port}/api/packages/"
            response = requests.get(url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                packages = response.json()
                for package in packages:
                    if package["is_active"]:
                        self.package_combo.addItem(package["name"], package["id"])
        except Exception as e:
            print(f"خطأ في تحميل الباقات: {e}")
    
    def load_subscribers(self):
        """تحميل قائمة المشتركين"""
        try:
            self.status_label.setText("جاري تحميل البيانات...")
            
            headers = {"Authorization": f"Bearer {self.access_token}"}
            url = f"http://{settings.api_host}:{settings.api_port}/api/subscribers/"
            response = requests.get(url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                self.subscribers_data = response.json()
                self.populate_table()
                self.status_label.setText(f"تم تحميل {len(self.subscribers_data)} مشترك")
            else:
                QMessageBox.warning(self, "خطأ", "فشل في تحميل بيانات المشتركين")
                self.status_label.setText("فشل في تحميل البيانات")
                
        except requests.exceptions.ConnectionError:
            QMessageBox.critical(self, "خطأ في الاتصال", "لا يمكن الاتصال بالخادم")
            self.status_label.setText("خطأ في الاتصال")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ غير متوقع: {str(e)}")
            self.status_label.setText("خطأ في التحميل")
    
    def populate_table(self, data=None):
        """ملء الجدول بالبيانات"""
        if data is None:
            data = self.subscribers_data
        
        self.table.setRowCount(len(data))
        
        for row, subscriber in enumerate(data):
            # الرقم
            self.table.setItem(row, 0, QTableWidgetItem(str(subscriber["id"])))
            
            # الاسم
            self.table.setItem(row, 1, QTableWidgetItem(subscriber["name"]))
            
            # رقم الهاتف
            self.table.setItem(row, 2, QTableWidgetItem(subscriber["phone"]))
            
            # الباقة
            package_name = subscriber.get("package_name", "غير محدد")
            self.table.setItem(row, 3, QTableWidgetItem(package_name))
            
            # تاريخ الانتهاء
            end_date = subscriber.get("subscription_end", "غير محدد")
            if end_date and end_date != "غير محدد":
                end_date = end_date.split("T")[0]  # إزالة الوقت
            self.table.setItem(row, 4, QTableWidgetItem(end_date))
            
            # الحالة
            status = "نشط" if subscriber["is_active"] else "غير نشط"
            status_item = QTableWidgetItem(status)
            if subscriber["is_active"]:
                status_item.setBackground(Qt.GlobalColor.green)
            else:
                status_item.setBackground(Qt.GlobalColor.red)
            self.table.setItem(row, 5, status_item)
            
            # الرصيد
            balance = f"{subscriber['balance']:.2f}"
            self.table.setItem(row, 6, QTableWidgetItem(balance))
            
            # تاريخ الإنشاء
            created_date = subscriber["created_at"].split("T")[0]
            self.table.setItem(row, 7, QTableWidgetItem(created_date))
    
    def filter_subscribers(self):
        """فلترة المشتركين"""
        search_text = self.search_input.text().lower()
        status_filter = self.status_combo.currentText()
        package_filter = self.package_combo.currentData()
        
        filtered_data = []
        
        for subscriber in self.subscribers_data:
            # فلتر البحث
            if search_text and search_text not in subscriber["name"].lower() and search_text not in subscriber["phone"]:
                continue
            
            # فلتر الحالة
            if status_filter == "نشط" and not subscriber["is_active"]:
                continue
            elif status_filter == "غير نشط" and subscriber["is_active"]:
                continue
            
            # فلتر الباقة
            if package_filter and subscriber.get("package_id") != package_filter:
                continue
            
            filtered_data.append(subscriber)
        
        self.populate_table(filtered_data)
        self.status_label.setText(f"عرض {len(filtered_data)} من {len(self.subscribers_data)} مشترك")
    
    def add_subscriber(self):
        """إضافة مشترك جديد"""
        dialog = AddSubscriberDialog(self.access_token, self)
        
        if dialog.exec() == QDialog.DialogCode.Accepted:
            data = dialog.get_data()
            
            try:
                headers = {"Authorization": f"Bearer {self.access_token}"}
                url = f"http://{settings.api_host}:{settings.api_port}/api/subscribers/"
                response = requests.post(url, json=data, headers=headers, timeout=10)
                
                if response.status_code == 200:
                    QMessageBox.information(self, "نجح", "تم إضافة المشترك بنجاح")
                    self.load_subscribers()  # تحديث القائمة
                else:
                    error_msg = "فشل في إضافة المشترك"
                    if response.status_code == 400:
                        error_data = response.json()
                        error_msg = error_data.get("detail", error_msg)
                    QMessageBox.warning(self, "خطأ", error_msg)
                    
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في إضافة المشترك: {str(e)}")
    
    def edit_subscriber(self):
        """تعديل مشترك"""
        current_row = self.table.currentRow()
        if current_row >= 0:
            subscriber_id = self.table.item(current_row, 0).text()
            QMessageBox.information(self, "تعديل مشترك", f"سيتم فتح نافذة تعديل المشترك رقم {subscriber_id}")
    
    def renew_subscription(self):
        """تجديد اشتراك"""
        current_row = self.table.currentRow()
        if current_row >= 0:
            subscriber_name = self.table.item(current_row, 1).text()
            QMessageBox.information(self, "تجديد اشتراك", f"سيتم فتح نافذة تجديد اشتراك {subscriber_name}")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار مشترك من القائمة")
    
    def print_list(self):
        """طباعة القائمة"""
        QMessageBox.information(self, "طباعة", "سيتم طباعة قائمة المشتركين")
