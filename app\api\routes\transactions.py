"""
مسارات إدارة المعاملات المالية
"""
from typing import List, Optional
from datetime import datetime, date
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import and_, func
from pydantic import BaseModel

from app.db.database import get_db
from app.services.auth import get_current_active_user, require_permission
from app.models.user import User
from app.models.transaction import Transaction, CashBox

router = APIRouter()


class TransactionCreate(BaseModel):
    """نموذج إنشاء معاملة"""
    type: str
    category: Optional[str] = None
    amount: float
    currency: str = "USD"
    payment_method: str = "cash"
    payment_reference: Optional[str] = None
    description: Optional[str] = None
    notes: Optional[str] = None
    subscriber_id: Optional[int] = None
    router_id: Optional[int] = None
    package_id: Optional[int] = None


class TransactionResponse(BaseModel):
    """استجابة المعاملة"""
    id: int
    transaction_number: str
    type: str
    category: Optional[str]
    amount: float
    currency: str
    payment_method: str
    payment_reference: Optional[str]
    description: Optional[str]
    notes: Optional[str]
    status: str
    subscriber_id: Optional[int]
    router_id: Optional[int]
    package_id: Optional[int]
    user_id: int
    transaction_date: datetime
    created_at: datetime
    
    # معلومات إضافية
    subscriber_name: Optional[str] = None
    user_name: Optional[str] = None

    class Config:
        from_attributes = True


class CashBoxResponse(BaseModel):
    """استجابة الصندوق"""
    id: int
    date: datetime
    user_id: int
    opening_cash: float
    opening_bank: float
    closing_cash: float
    closing_bank: float
    is_closed: bool
    closed_at: Optional[datetime]
    notes: Optional[str]
    
    # إحصائيات محسوبة
    total_income: float = 0.0
    total_expenses: float = 0.0
    net_amount: float = 0.0
    transactions_count: int = 0

    class Config:
        from_attributes = True


@router.get("/", response_model=List[TransactionResponse])
async def get_transactions(
    skip: int = 0,
    limit: int = 100,
    start_date: Optional[date] = Query(None, description="تاريخ البداية"),
    end_date: Optional[date] = Query(None, description="تاريخ النهاية"),
    transaction_type: Optional[str] = Query(None, description="نوع المعاملة"),
    payment_method: Optional[str] = Query(None, description="طريقة الدفع"),
    subscriber_id: Optional[int] = Query(None, description="المشترك"),
    current_user: User = Depends(require_permission("transactions")),
    db: Session = Depends(get_db)
):
    """الحصول على قائمة المعاملات"""
    query = db.query(Transaction)
    
    # فلترة حسب التاريخ
    if start_date:
        query = query.filter(func.date(Transaction.transaction_date) >= start_date)
    
    if end_date:
        query = query.filter(func.date(Transaction.transaction_date) <= end_date)
    
    # فلترة حسب النوع
    if transaction_type:
        query = query.filter(Transaction.type == transaction_type)
    
    # فلترة حسب طريقة الدفع
    if payment_method:
        query = query.filter(Transaction.payment_method == payment_method)
    
    # فلترة حسب المشترك
    if subscriber_id:
        query = query.filter(Transaction.subscriber_id == subscriber_id)
    
    transactions = query.order_by(Transaction.transaction_date.desc()).offset(skip).limit(limit).all()
    
    # إضافة معلومات إضافية
    result = []
    for transaction in transactions:
        transaction_data = TransactionResponse.from_orm(transaction)
        
        if transaction.subscriber:
            transaction_data.subscriber_name = transaction.subscriber.name
        
        if transaction.user:
            transaction_data.user_name = transaction.user.full_name
        
        result.append(transaction_data)
    
    return result


@router.get("/{transaction_id}", response_model=TransactionResponse)
async def get_transaction(
    transaction_id: int,
    current_user: User = Depends(require_permission("transactions")),
    db: Session = Depends(get_db)
):
    """الحصول على معاملة محددة"""
    transaction = db.query(Transaction).filter(Transaction.id == transaction_id).first()
    if not transaction:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="المعاملة غير موجودة"
        )
    
    transaction_data = TransactionResponse.from_orm(transaction)
    
    if transaction.subscriber:
        transaction_data.subscriber_name = transaction.subscriber.name
    
    if transaction.user:
        transaction_data.user_name = transaction.user.full_name
    
    return transaction_data


@router.post("/", response_model=TransactionResponse)
async def create_transaction(
    transaction_data: TransactionCreate,
    current_user: User = Depends(require_permission("transactions")),
    db: Session = Depends(get_db)
):
    """إنشاء معاملة جديدة"""
    # إنشاء رقم المعاملة
    transaction_number = f"{transaction_data.type.upper()}-{datetime.now().strftime('%Y%m%d%H%M%S')}"
    
    # إنشاء المعاملة
    transaction = Transaction(
        transaction_number=transaction_number,
        type=transaction_data.type,
        category=transaction_data.category,
        amount=transaction_data.amount,
        currency=transaction_data.currency,
        payment_method=transaction_data.payment_method,
        payment_reference=transaction_data.payment_reference,
        description=transaction_data.description,
        notes=transaction_data.notes,
        subscriber_id=transaction_data.subscriber_id,
        router_id=transaction_data.router_id,
        package_id=transaction_data.package_id,
        user_id=current_user.id
    )
    
    db.add(transaction)
    db.commit()
    db.refresh(transaction)
    
    return TransactionResponse.from_orm(transaction)


@router.get("/daily-summary/{date}")
async def get_daily_summary(
    date: date,
    current_user: User = Depends(require_permission("transactions")),
    db: Session = Depends(get_db)
):
    """الحصول على ملخص يومي للمعاملات"""
    # المعاملات اليومية
    transactions = db.query(Transaction).filter(
        func.date(Transaction.transaction_date) == date
    ).all()
    
    # حساب الإحصائيات
    total_income = sum(t.amount for t in transactions if t.is_income)
    total_expenses = sum(t.amount for t in transactions if t.is_expense)
    net_amount = total_income - total_expenses
    
    # تصنيف المعاملات
    income_by_type = {}
    expenses_by_type = {}
    
    for transaction in transactions:
        if transaction.is_income:
            income_by_type[transaction.type] = income_by_type.get(transaction.type, 0) + transaction.amount
        else:
            expenses_by_type[transaction.type] = expenses_by_type.get(transaction.type, 0) + transaction.amount
    
    return {
        "date": date,
        "total_transactions": len(transactions),
        "total_income": total_income,
        "total_expenses": total_expenses,
        "net_amount": net_amount,
        "income_by_type": income_by_type,
        "expenses_by_type": expenses_by_type,
        "transactions": [TransactionResponse.from_orm(t) for t in transactions]
    }


@router.get("/cash-boxes/", response_model=List[CashBoxResponse])
async def get_cash_boxes(
    skip: int = 0,
    limit: int = 100,
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    current_user: User = Depends(require_permission("cash_box")),
    db: Session = Depends(get_db)
):
    """الحصول على قائمة الصناديق"""
    query = db.query(CashBox)
    
    if start_date:
        query = query.filter(func.date(CashBox.date) >= start_date)
    
    if end_date:
        query = query.filter(func.date(CashBox.date) <= end_date)
    
    cash_boxes = query.order_by(CashBox.date.desc()).offset(skip).limit(limit).all()
    
    # إضافة الإحصائيات
    result = []
    for cash_box in cash_boxes:
        cash_box_data = CashBoxResponse.from_orm(cash_box)
        cash_box_data.total_income = cash_box.total_income
        cash_box_data.total_expenses = cash_box.total_expenses
        cash_box_data.net_amount = cash_box.net_amount
        cash_box_data.transactions_count = len(cash_box.transactions)
        result.append(cash_box_data)
    
    return result


@router.post("/cash-boxes/open")
async def open_cash_box(
    opening_cash: float = 0.0,
    opening_bank: float = 0.0,
    notes: Optional[str] = None,
    current_user: User = Depends(require_permission("cash_box")),
    db: Session = Depends(get_db)
):
    """فتح صندوق جديد"""
    # التحقق من عدم وجود صندوق مفتوح
    today = datetime.now().date()
    existing_box = db.query(CashBox).filter(
        and_(
            func.date(CashBox.date) == today,
            CashBox.user_id == current_user.id,
            CashBox.is_closed == False
        )
    ).first()
    
    if existing_box:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="يوجد صندوق مفتوح بالفعل لهذا اليوم"
        )
    
    # إنشاء صندوق جديد
    cash_box = CashBox(
        date=datetime.now(),
        user_id=current_user.id,
        opening_cash=opening_cash,
        opening_bank=opening_bank,
        notes=notes
    )
    
    db.add(cash_box)
    db.commit()
    db.refresh(cash_box)
    
    return {
        "message": "تم فتح الصندوق بنجاح",
        "cash_box_id": cash_box.id,
        "date": cash_box.date
    }


@router.post("/cash-boxes/{cash_box_id}/close")
async def close_cash_box(
    cash_box_id: int,
    closing_cash: float,
    closing_bank: float,
    notes: Optional[str] = None,
    current_user: User = Depends(require_permission("cash_box")),
    db: Session = Depends(get_db)
):
    """إغلاق الصندوق"""
    cash_box = db.query(CashBox).filter(CashBox.id == cash_box_id).first()
    if not cash_box:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="الصندوق غير موجود"
        )
    
    if cash_box.is_closed:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="الصندوق مغلق بالفعل"
        )
    
    # إغلاق الصندوق
    cash_box.closing_cash = closing_cash
    cash_box.closing_bank = closing_bank
    cash_box.is_closed = True
    cash_box.closed_at = datetime.now()
    
    if notes:
        cash_box.notes = notes
    
    db.commit()
    
    return {
        "message": "تم إغلاق الصندوق بنجاح",
        "total_income": cash_box.total_income,
        "total_expenses": cash_box.total_expenses,
        "net_amount": cash_box.net_amount
    }
