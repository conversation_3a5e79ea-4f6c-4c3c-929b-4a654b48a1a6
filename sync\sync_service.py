"""
خدمة المزامنة التلقائية
"""
import threading
import time
import schedule
from datetime import datetime, timedelta
from typing import Optional, Callable

from app.core.config import settings
from sync.google_drive.drive_manager import GoogleDriveManager


class SyncService:
    """خدمة المزامنة التلقائية"""
    
    def __init__(self):
        self.drive_manager = GoogleDriveManager()
        self.is_running = False
        self.sync_thread = None
        self.last_sync = None
        self.sync_interval = settings.sync_interval_minutes
        self.auto_backup_enabled = settings.auto_backup_enabled
        self.auto_sync_enabled = settings.auto_sync_enabled
        self.offline_mode = settings.offline_mode
        
        # callbacks للأحداث
        self.on_sync_start: Optional[Callable] = None
        self.on_sync_complete: Optional[Callable] = None
        self.on_sync_error: Optional[Callable] = None
        self.on_backup_complete: Optional[Callable] = None
        
    def start(self):
        """بدء خدمة المزامنة"""
        if self.is_running:
            print("خدمة المزامنة تعمل بالفعل")
            return

        if self.offline_mode:
            print("الوضع غير المتصل مفعل، لن يتم بدء المزامنة")
            return

        try:
            self.is_running = True

            # مسح الجدولة السابقة
            schedule.clear()

            # جدولة المزامنة التلقائية
            if self.auto_sync_enabled:
                schedule.every(self.sync_interval).minutes.do(self._sync_job).tag('sync')
                print(f"تم جدولة المزامنة التلقائية كل {self.sync_interval} دقيقة")

            # جدولة النسخ الاحتياطي
            if self.auto_backup_enabled:
                schedule.every().day.at("23:59").do(self._backup_job).tag('backup')
                print("تم جدولة النسخ الاحتياطي اليومي في 23:59")

            # بدء خيط المزامنة
            if not self.sync_thread or not self.sync_thread.is_alive():
                self.sync_thread = threading.Thread(target=self._run_scheduler, daemon=True)
                self.sync_thread.start()

            print("تم بدء خدمة المزامنة")

            # مزامنة أولية (في الخلفية)
            threading.Thread(target=self._initial_sync, daemon=True).start()

        except Exception as e:
            print(f"خطأ في بدء خدمة المزامنة: {e}")
            self.is_running = False
    
    def stop(self):
        """إيقاف خدمة المزامنة"""
        self.is_running = False
        schedule.clear()
        
        if self.sync_thread and self.sync_thread.is_alive():
            self.sync_thread.join(timeout=5)
        
        print("تم إيقاف خدمة المزامنة")
    
    def _run_scheduler(self):
        """تشغيل جدولة المهام"""
        while self.is_running:
            try:
                schedule.run_pending()
                time.sleep(1)
            except Exception as e:
                print(f"خطأ في جدولة المهام: {e}")
                time.sleep(5)
    
    def _sync_job(self):
        """مهمة المزامنة المجدولة"""
        if not self.is_running:
            return
        
        try:
            print(f"بدء المزامنة التلقائية - {datetime.now()}")
            self.sync_now()
        except Exception as e:
            print(f"خطأ في المزامنة التلقائية: {e}")
    
    def _backup_job(self):
        """مهمة النسخ الاحتياطي المجدولة"""
        if not self.is_running:
            return

        try:
            print(f"بدء النسخ الاحتياطي التلقائي - {datetime.now()}")
            self.backup_now()
        except Exception as e:
            print(f"خطأ في النسخ الاحتياطي التلقائي: {e}")

    def _initial_sync(self):
        """مزامنة أولية في الخلفية"""
        try:
            import time
            time.sleep(5)  # انتظار 5 ثوان قبل المزامنة الأولية
            if self.is_running:
                self.sync_now()
        except Exception as e:
            print(f"خطأ في المزامنة الأولية: {e}")
    
    def sync_now(self) -> bool:
        """مزامنة فورية مع مقاومة الأخطاء"""
        if self.offline_mode:
            print("الوضع غير المتصل مفعل، لا يمكن المزامنة")
            return False

        max_retries = 3
        retry_delay = 5

        for attempt in range(max_retries):
            try:
                if self.on_sync_start:
                    self.on_sync_start()

                print(f"بدء المزامنة... (المحاولة {attempt + 1}/{max_retries})")

                # التحقق من الاتصال مع timeout
                if not self._check_connection_with_timeout():
                    print("لا يمكن الاتصال بـ Google Drive")
                    if attempt < max_retries - 1:
                        print(f"إعادة المحاولة خلال {retry_delay} ثانية...")
                        import time
                        time.sleep(retry_delay)
                        continue
                    else:
                        if self.on_sync_error:
                            self.on_sync_error("لا يمكن الاتصال بـ Google Drive")
                        return False

                # مزامنة قاعدة البيانات
                success = self.drive_manager.sync_database()

                if success:
                    self.last_sync = datetime.now()
                    print(f"تمت المزامنة بنجاح في {self.last_sync}")

                    if self.on_sync_complete:
                        self.on_sync_complete()
                    return True
                else:
                    print("فشلت المزامنة")
                    if attempt < max_retries - 1:
                        print(f"إعادة المحاولة خلال {retry_delay} ثانية...")
                        import time
                        time.sleep(retry_delay)
                        continue
                    else:
                        if self.on_sync_error:
                            self.on_sync_error("فشلت المزامنة")
                        return False

            except Exception as e:
                error_msg = f"خطأ في المزامنة: {e}"
                print(error_msg)
                if attempt < max_retries - 1:
                    print(f"إعادة المحاولة خلال {retry_delay} ثانية...")
                    import time
                    time.sleep(retry_delay)
                    continue
                else:
                    if self.on_sync_error:
                        self.on_sync_error(error_msg)
                    return False

        return False

    def _check_connection_with_timeout(self, timeout: int = 10) -> bool:
        """فحص الاتصال مع timeout"""
        try:
            import socket
            import threading

            result = [False]

            def check():
                try:
                    # محاولة الاتصال بـ Google DNS
                    socket.create_connection(("8.8.8.8", 53), timeout=5)
                    result[0] = True
                except:
                    try:
                        # محاولة الاتصال بـ Cloudflare DNS
                        socket.create_connection(("1.1.1.1", 53), timeout=5)
                        result[0] = True
                    except:
                        result[0] = False

            thread = threading.Thread(target=check)
            thread.daemon = True
            thread.start()
            thread.join(timeout)

            if thread.is_alive():
                print("انتهت مهلة فحص الاتصال")
                return False

            if result[0]:
                # إذا كان الإنترنت متاح، فحص Google Drive
                return self.drive_manager.is_connected()
            else:
                print("لا يوجد اتصال بالإنترنت")
                return False

        except Exception as e:
            print(f"خطأ في فحص الاتصال: {e}")
            return False

    def backup_now(self) -> bool:
        """نسخ احتياطي فوري"""
        if self.offline_mode:
            print("الوضع غير المتصل مفعل، لا يمكن النسخ الاحتياطي")
            return False
        
        try:
            print("بدء النسخ الاحتياطي...")
            
            # التحقق من الاتصال
            if not self.drive_manager.is_connected():
                print("لا يمكن الاتصال بـ Google Drive")
                return False
            
            # رفع قاعدة البيانات
            success = self.drive_manager.upload_database()
            
            if success:
                print("تم النسخ الاحتياطي بنجاح")
                
                # تنظيف النسخ القديمة
                self.drive_manager.cleanup_old_backups(keep_count=10)
                
                if self.on_backup_complete:
                    self.on_backup_complete()
            else:
                print("فشل النسخ الاحتياطي")
            
            return success
            
        except Exception as e:
            print(f"خطأ في النسخ الاحتياطي: {e}")
            return False
    
    def download_latest(self) -> bool:
        """تحميل أحدث نسخة من Google Drive"""
        if self.offline_mode:
            print("الوضع غير المتصل مفعل، لا يمكن التحميل")
            return False
        
        try:
            print("تحميل أحدث نسخة من Google Drive...")
            
            if not self.drive_manager.is_connected():
                print("لا يمكن الاتصال بـ Google Drive")
                return False
            
            success = self.drive_manager.download_database()
            
            if success:
                print("تم تحميل أحدث نسخة بنجاح")
            else:
                print("فشل تحميل أحدث نسخة")
            
            return success
            
        except Exception as e:
            print(f"خطأ في تحميل أحدث نسخة: {e}")
            return False
    
    def get_sync_status(self) -> dict:
        """الحصول على حالة المزامنة"""
        return {
            "is_running": self.is_running,
            "last_sync": self.last_sync.isoformat() if self.last_sync else None,
            "auto_sync_enabled": self.auto_sync_enabled,
            "auto_backup_enabled": self.auto_backup_enabled,
            "offline_mode": self.offline_mode,
            "sync_interval": self.sync_interval,
            "is_connected": self.drive_manager.is_connected() if not self.offline_mode else False
        }
    
    def set_sync_interval(self, minutes: int):
        """تعديل فترة المزامنة"""
        self.sync_interval = minutes
        
        # إعادة جدولة المزامنة
        if self.is_running and self.auto_sync_enabled:
            schedule.clear('sync')
            schedule.every(self.sync_interval).minutes.do(self._sync_job).tag('sync')
            print(f"تم تعديل فترة المزامنة إلى {minutes} دقيقة")
    
    def enable_auto_sync(self, enabled: bool):
        """تفعيل/إلغاء المزامنة التلقائية"""
        self.auto_sync_enabled = enabled
        
        if enabled and self.is_running:
            schedule.every(self.sync_interval).minutes.do(self._sync_job).tag('sync')
            print("تم تفعيل المزامنة التلقائية")
        else:
            schedule.clear('sync')
            print("تم إلغاء المزامنة التلقائية")
    
    def enable_auto_backup(self, enabled: bool):
        """تفعيل/إلغاء النسخ الاحتياطي التلقائي"""
        self.auto_backup_enabled = enabled
        
        if enabled and self.is_running:
            schedule.every().day.at("23:59").do(self._backup_job).tag('backup')
            print("تم تفعيل النسخ الاحتياطي التلقائي")
        else:
            schedule.clear('backup')
            print("تم إلغاء النسخ الاحتياطي التلقائي")
    
    def set_offline_mode(self, offline: bool):
        """تفعيل/إلغاء الوضع غير المتصل"""
        self.offline_mode = offline
        
        if offline:
            print("تم تفعيل الوضع غير المتصل")
            if self.is_running:
                self.stop()
        else:
            print("تم إلغاء الوضع غير المتصل")
            if not self.is_running:
                self.start()
    
    def get_backups_list(self) -> list:
        """الحصول على قائمة النسخ الاحتياطية"""
        if self.offline_mode:
            return []
        
        try:
            return self.drive_manager.list_backups()
        except Exception as e:
            print(f"خطأ في الحصول على قائمة النسخ الاحتياطية: {e}")
            return []
    
    def restore_backup(self, backup_id: str) -> bool:
        """استعادة نسخة احتياطية"""
        if self.offline_mode:
            print("الوضع غير المتصل مفعل، لا يمكن الاستعادة")
            return False
        
        try:
            print(f"استعادة النسخة الاحتياطية: {backup_id}")
            return self.drive_manager.restore_backup(backup_id)
        except Exception as e:
            print(f"خطأ في استعادة النسخة الاحتياطية: {e}")
            return False


# مثيل عام للخدمة
sync_service = SyncService()
