"""
نموذج المستخدمين
"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text
from sqlalchemy.sql import func
from app.db.database import Base


class User(Base):
    """نموذج المستخدم"""
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    full_name = Column(String(100), nullable=False)
    hashed_password = Column(String(255), nullable=False)
    role = Column(String(20), nullable=False, default="user")  # admin, user, cashier
    is_active = Column(Boolean, default=True)
    phone = Column(String(20), nullable=True)
    address = Column(Text, nullable=True)
    notes = Column(Text, nullable=True)
    
    # تواريخ النظام
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_login = Column(DateTime(timezone=True), nullable=True)
    
    def __repr__(self):
        return f"<User(username='{self.username}', role='{self.role}')>"
    
    @property
    def is_admin(self):
        """التحقق من كون المستخدم مدير"""
        return self.role == "admin"
    
    @property
    def is_cashier(self):
        """التحقق من كون المستخدم كاشير"""
        return self.role == "cashier"
    
    def can_access(self, resource: str) -> bool:
        """التحقق من صلاحية الوصول لمورد معين"""
        if not self.is_active:
            return False
            
        if self.is_admin:
            return True
            
        # صلاحيات الكاشير
        if self.role == "cashier":
            cashier_resources = [
                "sales", "subscribers", "routers", 
                "daily_reports", "print_receipts"
            ]
            return resource in cashier_resources
            
        # صلاحيات المستخدم العادي
        if self.role == "user":
            user_resources = [
                "subscribers", "routers", "packages",
                "basic_reports", "print_receipts"
            ]
            return resource in user_resources
            
        return False
