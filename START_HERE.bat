@echo off
chcp 65001 >nul 2>&1
title Accounting App - Main Launcher

cls
echo ================================================================
echo 🏢 Accounting Application - Main Launcher
echo ================================================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo 📥 Please download Python from: https://python.org/downloads
    echo.
    pause
    exit /b 1
)

echo ✅ Python is available
python --version

REM Check if virtual environment exists
if not exist "venv\Scripts\python.exe" (
    echo.
    echo ❌ Virtual environment not found
    echo 🔧 Please run install_requirements.bat first
    echo.
    pause
    exit /b 1
)

echo ✅ Virtual environment found

echo.
echo 📋 Choose how to start the application:
echo.
echo 1. Complete Application (API + GUI)
echo 2. API Server Only
echo 3. Desktop GUI Only  
echo 4. Web Interface Only
echo 5. Install Requirements
echo 6. Exit
echo.

set /p choice="Enter your choice (1-6): "

if "%choice%"=="1" goto :complete
if "%choice%"=="2" goto :api_only
if "%choice%"=="3" goto :gui_only
if "%choice%"=="4" goto :web_only
if "%choice%"=="5" goto :install
if "%choice%"=="6" goto :exit
goto :invalid

:complete
echo.
echo 🚀 Starting Complete Application...
echo.
start "API Server" cmd /c "venv\Scripts\python start_api_only.py"
timeout /t 5 /nobreak >nul
echo 🖥️ Starting Desktop GUI...
venv\Scripts\python start_gui_only.py
goto :end

:api_only
echo.
echo 🌐 Starting API Server...
echo 📚 Docs will be available at: http://localhost:8000/docs
echo.
venv\Scripts\python start_api_only.py
goto :end

:gui_only
echo.
echo 🖥️ Starting Desktop GUI...
echo 💡 Make sure API server is running first
echo.
venv\Scripts\python start_gui_only.py
goto :end

:web_only
echo.
echo 🌐 Starting API Server for Web Interface...
start "API Server" cmd /c "venv\Scripts\python start_api_only.py"
timeout /t 3 /nobreak >nul
echo 🌐 Opening web browser...
start http://localhost:8000/docs
echo.
echo ✅ Web interface opened in browser
echo 🔑 Login credentials: admin / admin123
goto :end

:install
echo.
echo 📦 Running installation...
call install_requirements.bat
goto :menu

:invalid
echo.
echo ❌ Invalid choice. Please enter 1-6.
echo.
pause
goto :menu

:exit
echo.
echo 👋 Goodbye!
exit /b 0

:end
echo.
echo ================================================================
echo 👋 Application finished
echo ================================================================
pause

:menu
goto :start
