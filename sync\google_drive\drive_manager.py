"""
مدير مزامنة Google Drive
"""
import os
import json
import shutil
import sqlite3
from datetime import datetime
from typing import Optional, List, Dict, Any
from pathlib import Path

from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.http import MediaFileUpload, MediaIoBaseDownload
import io

from app.core.config import settings


class GoogleDriveManager:
    """مدير مزامنة Google Drive"""
    
    SCOPES = ['https://www.googleapis.com/auth/drive.file']
    
    def __init__(self):
        self.service = None
        self.credentials = None
        self.folder_id = settings.google_drive_folder_id
        self.credentials_file = settings.google_drive_credentials_file
        self.token_file = "config/token.json"
        
    def authenticate(self) -> bool:
        """مصادقة Google Drive مع مقاومة الأخطاء"""
        max_retries = 3
        retry_delay = 2

        for attempt in range(max_retries):
            try:
                creds = None

                # تحميل الرموز المحفوظة
                if os.path.exists(self.token_file):
                    try:
                        creds = Credentials.from_authorized_user_file(self.token_file, self.SCOPES)
                    except Exception as e:
                        print(f"خطأ في تحميل الرموز المحفوظة: {e}")
                        # حذف الملف التالف
                        try:
                            os.remove(self.token_file)
                        except:
                            pass
                        creds = None

                # إذا لم تكن هناك رموز صالحة، طلب المصادقة
                if not creds or not creds.valid:
                    if creds and creds.expired and creds.refresh_token:
                        try:
                            # إعداد timeout للطلبات
                            import socket
                            socket.setdefaulttimeout(30)
                            creds.refresh(Request())
                        except Exception as e:
                            print(f"فشل في تحديث الرموز: {e}")
                            if attempt < max_retries - 1:
                                print(f"إعادة المحاولة خلال {retry_delay} ثانية...")
                                import time
                                time.sleep(retry_delay)
                                continue
                            return False
                    else:
                        if not os.path.exists(self.credentials_file):
                            print(f"ملف الاعتماد غير موجود: {self.credentials_file}")
                            print("يرجى إعداد Google Drive credentials أولاً")
                            return False

                        try:
                            flow = InstalledAppFlow.from_client_secrets_file(
                                self.credentials_file, self.SCOPES)
                            # تشغيل المصادقة مع timeout
                            creds = flow.run_local_server(port=0, timeout_seconds=60)
                        except Exception as e:
                            print(f"فشل في المصادقة: {e}")
                            if attempt < max_retries - 1:
                                print(f"إعادة المحاولة خلال {retry_delay} ثانية...")
                                import time
                                time.sleep(retry_delay)
                                continue
                            return False

                    # حفظ الرموز للاستخدام التالي
                    try:
                        os.makedirs(os.path.dirname(self.token_file), exist_ok=True)
                        with open(self.token_file, 'w') as token:
                            token.write(creds.to_json())
                    except Exception as e:
                        print(f"تحذير: لم يتم حفظ الرموز: {e}")

                # إنشاء خدمة Google Drive
                try:
                    self.service = build('drive', 'v3', credentials=creds, cache_discovery=False)
                    self.credentials = creds

                    # اختبار الاتصال
                    self.service.files().list(pageSize=1).execute()
                    return True

                except Exception as e:
                    print(f"فشل في إنشاء خدمة Google Drive: {e}")
                    if attempt < max_retries - 1:
                        print(f"إعادة المحاولة خلال {retry_delay} ثانية...")
                        import time
                        time.sleep(retry_delay)
                        continue
                    return False

            except Exception as e:
                print(f"خطأ في مصادقة Google Drive (المحاولة {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    print(f"إعادة المحاولة خلال {retry_delay} ثانية...")
                    import time
                    time.sleep(retry_delay)
                    continue
                return False

        return False
    
    def create_app_folder(self) -> Optional[str]:
        """إنشاء مجلد التطبيق في Google Drive"""
        try:
            if not self.service:
                if not self.authenticate():
                    return None
            
            # البحث عن مجلد التطبيق
            folder_name = "Accounting App Data"
            query = f"name='{folder_name}' and mimeType='application/vnd.google-apps.folder'"
            results = self.service.files().list(q=query).execute()
            items = results.get('files', [])
            
            if items:
                # المجلد موجود
                folder_id = items[0]['id']
                print(f"تم العثور على مجلد التطبيق: {folder_id}")
            else:
                # إنشاء مجلد جديد
                folder_metadata = {
                    'name': folder_name,
                    'mimeType': 'application/vnd.google-apps.folder'
                }
                
                folder = self.service.files().create(body=folder_metadata, fields='id').execute()
                folder_id = folder.get('id')
                print(f"تم إنشاء مجلد التطبيق: {folder_id}")
            
            self.folder_id = folder_id
            return folder_id
            
        except Exception as e:
            print(f"خطأ في إنشاء مجلد التطبيق: {e}")
            return None
    
    def upload_database(self, db_path: str = None) -> bool:
        """رفع قاعدة البيانات إلى Google Drive"""
        try:
            if not self.service:
                if not self.authenticate():
                    return False
            
            if not self.folder_id:
                self.folder_id = self.create_app_folder()
                if not self.folder_id:
                    return False
            
            # مسار قاعدة البيانات
            if not db_path:
                db_path = "data/accounting.db"
            
            if not os.path.exists(db_path):
                print(f"قاعدة البيانات غير موجودة: {db_path}")
                return False
            
            # إنشاء نسخة احتياطية مع الطابع الزمني
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"accounting_backup_{timestamp}.db"
            
            # البحث عن ملف قاعدة البيانات الموجود
            query = f"name='accounting.db' and parents in '{self.folder_id}'"
            results = self.service.files().list(q=query).execute()
            items = results.get('files', [])
            
            # رفع الملف
            media = MediaFileUpload(db_path, mimetype='application/octet-stream')
            
            if items:
                # تحديث الملف الموجود
                file_id = items[0]['id']
                updated_file = self.service.files().update(
                    fileId=file_id,
                    media_body=media
                ).execute()
                print(f"تم تحديث قاعدة البيانات: {updated_file.get('id')}")
            else:
                # إنشاء ملف جديد
                file_metadata = {
                    'name': 'accounting.db',
                    'parents': [self.folder_id]
                }
                
                uploaded_file = self.service.files().create(
                    body=file_metadata,
                    media_body=media,
                    fields='id'
                ).execute()
                print(f"تم رفع قاعدة البيانات: {uploaded_file.get('id')}")
            
            # رفع نسخة احتياطية إضافية
            backup_metadata = {
                'name': backup_filename,
                'parents': [self.folder_id]
            }
            
            backup_media = MediaFileUpload(db_path, mimetype='application/octet-stream')
            backup_file = self.service.files().create(
                body=backup_metadata,
                media_body=backup_media,
                fields='id'
            ).execute()
            print(f"تم رفع النسخة الاحتياطية: {backup_file.get('id')}")
            
            return True
            
        except Exception as e:
            print(f"خطأ في رفع قاعدة البيانات: {e}")
            return False
    
    def download_database(self, db_path: str = None) -> bool:
        """تحميل قاعدة البيانات من Google Drive"""
        try:
            if not self.service:
                if not self.authenticate():
                    return False
            
            if not self.folder_id:
                print("معرف المجلد غير محدد")
                return False
            
            # البحث عن ملف قاعدة البيانات
            query = f"name='accounting.db' and parents in '{self.folder_id}'"
            results = self.service.files().list(q=query).execute()
            items = results.get('files', [])
            
            if not items:
                print("لم يتم العثور على قاعدة البيانات في Google Drive")
                return False
            
            file_id = items[0]['id']
            
            # تحميل الملف
            request = self.service.files().get_media(fileId=file_id)
            file_io = io.BytesIO()
            downloader = MediaIoBaseDownload(file_io, request)
            
            done = False
            while done is False:
                status, done = downloader.next_chunk()
                print(f"تقدم التحميل: {int(status.progress() * 100)}%")
            
            # حفظ الملف محلياً
            if not db_path:
                db_path = "data/accounting.db"
            
            # إنشاء نسخة احتياطية من الملف المحلي إذا كان موجوداً
            if os.path.exists(db_path):
                backup_path = f"{db_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                shutil.copy2(db_path, backup_path)
                print(f"تم إنشاء نسخة احتياطية محلية: {backup_path}")
            
            # كتابة الملف الجديد
            with open(db_path, 'wb') as f:
                f.write(file_io.getvalue())
            
            print(f"تم تحميل قاعدة البيانات إلى: {db_path}")
            return True
            
        except Exception as e:
            print(f"خطأ في تحميل قاعدة البيانات: {e}")
            return False
    
    def sync_database(self) -> bool:
        """مزامنة قاعدة البيانات (تحميل أو رفع حسب الأحدث)"""
        try:
            if not self.service:
                if not self.authenticate():
                    return False
            
            local_db_path = "data/accounting.db"
            
            # التحقق من وجود الملف المحلي
            local_exists = os.path.exists(local_db_path)
            local_modified = datetime.fromtimestamp(os.path.getmtime(local_db_path)) if local_exists else None
            
            # البحث عن الملف في Google Drive
            if not self.folder_id:
                self.folder_id = self.create_app_folder()
            
            query = f"name='accounting.db' and parents in '{self.folder_id}'"
            results = self.service.files().list(q=query, fields='files(id,modifiedTime)').execute()
            items = results.get('files', [])
            
            remote_exists = len(items) > 0
            remote_modified = None
            
            if remote_exists:
                remote_modified_str = items[0]['modifiedTime']
                # تحويل التاريخ من ISO format
                remote_modified = datetime.fromisoformat(remote_modified_str.replace('Z', '+00:00')).replace(tzinfo=None)
            
            # تحديد اتجاه المزامنة
            if not local_exists and not remote_exists:
                print("لا توجد قاعدة بيانات محلية أو بعيدة")
                return False
            elif not local_exists and remote_exists:
                print("تحميل قاعدة البيانات من Google Drive")
                return self.download_database(local_db_path)
            elif local_exists and not remote_exists:
                print("رفع قاعدة البيانات إلى Google Drive")
                return self.upload_database(local_db_path)
            else:
                # كلاهما موجود، مقارنة التواريخ
                if local_modified > remote_modified:
                    print("الملف المحلي أحدث، رفع إلى Google Drive")
                    return self.upload_database(local_db_path)
                elif remote_modified > local_modified:
                    print("الملف البعيد أحدث، تحميل من Google Drive")
                    return self.download_database(local_db_path)
                else:
                    print("الملفات متطابقة، لا حاجة للمزامنة")
                    return True
            
        except Exception as e:
            print(f"خطأ في مزامنة قاعدة البيانات: {e}")
            return False
    
    def list_backups(self) -> List[Dict[str, Any]]:
        """قائمة النسخ الاحتياطية"""
        try:
            if not self.service:
                if not self.authenticate():
                    return []
            
            if not self.folder_id:
                return []
            
            # البحث عن ملفات النسخ الاحتياطية
            query = f"name contains 'accounting_backup_' and parents in '{self.folder_id}'"
            results = self.service.files().list(
                q=query,
                fields='files(id,name,modifiedTime,size)',
                orderBy='modifiedTime desc'
            ).execute()
            
            backups = []
            for item in results.get('files', []):
                backups.append({
                    'id': item['id'],
                    'name': item['name'],
                    'modified_time': item['modifiedTime'],
                    'size': item.get('size', 0)
                })
            
            return backups
            
        except Exception as e:
            print(f"خطأ في الحصول على قائمة النسخ الاحتياطية: {e}")
            return []
    
    def restore_backup(self, backup_id: str, local_path: str = None) -> bool:
        """استعادة نسخة احتياطية"""
        try:
            if not self.service:
                if not self.authenticate():
                    return False
            
            # تحميل النسخة الاحتياطية
            request = self.service.files().get_media(fileId=backup_id)
            file_io = io.BytesIO()
            downloader = MediaIoBaseDownload(file_io, request)
            
            done = False
            while done is False:
                status, done = downloader.next_chunk()
                print(f"تقدم الاستعادة: {int(status.progress() * 100)}%")
            
            # حفظ الملف
            if not local_path:
                local_path = "data/accounting.db"
            
            # إنشاء نسخة احتياطية من الملف الحالي
            if os.path.exists(local_path):
                backup_path = f"{local_path}.before_restore_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                shutil.copy2(local_path, backup_path)
                print(f"تم إنشاء نسخة احتياطية قبل الاستعادة: {backup_path}")
            
            # كتابة الملف المستعاد
            with open(local_path, 'wb') as f:
                f.write(file_io.getvalue())
            
            print(f"تم استعادة النسخة الاحتياطية إلى: {local_path}")
            return True
            
        except Exception as e:
            print(f"خطأ في استعادة النسخة الاحتياطية: {e}")
            return False
    
    def cleanup_old_backups(self, keep_count: int = 10) -> bool:
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            backups = self.list_backups()
            
            if len(backups) <= keep_count:
                print(f"عدد النسخ الاحتياطية ({len(backups)}) أقل من الحد المطلوب ({keep_count})")
                return True
            
            # حذف النسخ الزائدة
            backups_to_delete = backups[keep_count:]
            
            for backup in backups_to_delete:
                try:
                    self.service.files().delete(fileId=backup['id']).execute()
                    print(f"تم حذف النسخة الاحتياطية: {backup['name']}")
                except Exception as e:
                    print(f"خطأ في حذف النسخة الاحتياطية {backup['name']}: {e}")
            
            print(f"تم تنظيف {len(backups_to_delete)} نسخة احتياطية قديمة")
            return True
            
        except Exception as e:
            print(f"خطأ في تنظيف النسخ الاحتياطية: {e}")
            return False
    
    def is_connected(self) -> bool:
        """التحقق من الاتصال بـ Google Drive"""
        try:
            if not self.service:
                return self.authenticate()
            
            # اختبار بسيط للاتصال
            self.service.files().list(pageSize=1).execute()
            return True
            
        except Exception as e:
            print(f"خطأ في الاتصال بـ Google Drive: {e}")
            return False
