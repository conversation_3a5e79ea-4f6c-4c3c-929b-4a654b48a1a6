"""
واجهة إدارة الراوترات
"""
import requests
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QPushButton, QLineEdit, QLabel, QMessageBox, QHeaderView, QFrame,
    QComboBox, QDialog, QFormLayout, QDialogButtonBox, QDoubleSpinBox,
    QDateEdit, QTextEdit
)
from PyQt6.QtCore import Qt, QDate
from PyQt6.QtGui import QFont

from app.core.config import settings


class AddRouterDialog(QDialog):
    """نافذة إضافة راوتر جديد"""
    
    def __init__(self, access_token, parent=None):
        super().__init__(parent)
        self.access_token = access_token
        self.init_ui()
    
    def init_ui(self):
        """تهيئة واجهة النافذة"""
        self.setWindowTitle("إضافة راوتر جديد")
        self.setModal(True)
        self.resize(400, 500)
        
        layout = QVBoxLayout(self)
        
        # نموذج البيانات
        form_layout = QFormLayout()
        
        # الرقم التسلسلي
        self.serial_input = QLineEdit()
        self.serial_input.setPlaceholderText("أدخل الرقم التسلسلي")
        form_layout.addRow("الرقم التسلسلي:", self.serial_input)
        
        # الموديل
        self.model_input = QLineEdit()
        self.model_input.setPlaceholderText("أدخل موديل الراوتر")
        form_layout.addRow("الموديل:", self.model_input)
        
        # الماركة
        self.brand_input = QLineEdit()
        self.brand_input.setPlaceholderText("أدخل ماركة الراوتر")
        form_layout.addRow("الماركة:", self.brand_input)
        
        # سعر الشراء
        self.price_input = QDoubleSpinBox()
        self.price_input.setRange(0, 999999)
        self.price_input.setDecimals(2)
        self.price_input.setSuffix(" USD")
        form_layout.addRow("سعر الشراء:", self.price_input)
        
        # تاريخ الشراء
        self.purchase_date_input = QDateEdit()
        self.purchase_date_input.setDate(QDate.currentDate())
        self.purchase_date_input.setCalendarPopup(True)
        form_layout.addRow("تاريخ الشراء:", self.purchase_date_input)
        
        # المورد
        self.supplier_input = QLineEdit()
        self.supplier_input.setPlaceholderText("أدخل اسم المورد (اختياري)")
        form_layout.addRow("المورد:", self.supplier_input)
        
        # الحالة
        self.condition_combo = QComboBox()
        self.condition_combo.addItems(["new", "used", "refurbished"])
        form_layout.addRow("الحالة:", self.condition_combo)
        
        # عنوان MAC
        self.mac_input = QLineEdit()
        self.mac_input.setPlaceholderText("XX:XX:XX:XX:XX:XX (اختياري)")
        form_layout.addRow("عنوان MAC:", self.mac_input)
        
        # إصدار البرنامج الثابت
        self.firmware_input = QLineEdit()
        self.firmware_input.setPlaceholderText("إصدار البرنامج الثابت (اختياري)")
        form_layout.addRow("إصدار البرنامج:", self.firmware_input)
        
        # ملاحظات
        self.notes_input = QTextEdit()
        self.notes_input.setPlaceholderText("ملاحظات إضافية (اختياري)")
        self.notes_input.setMaximumHeight(60)
        form_layout.addRow("ملاحظات:", self.notes_input)
        
        layout.addLayout(form_layout)
        
        # أزرار التحكم
        buttons = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
    
    def get_data(self):
        """الحصول على بيانات النموذج"""
        return {
            "serial_number": self.serial_input.text().strip(),
            "model": self.model_input.text().strip(),
            "brand": self.brand_input.text().strip(),
            "purchase_price": self.price_input.value(),
            "purchase_date": self.purchase_date_input.date().toString(Qt.DateFormat.ISODate),
            "supplier": self.supplier_input.text().strip() or None,
            "condition": self.condition_combo.currentText(),
            "mac_address": self.mac_input.text().strip() or None,
            "firmware_version": self.firmware_input.text().strip() or None,
            "notes": self.notes_input.toPlainText().strip() or None
        }
    
    def validate_data(self):
        """التحقق من صحة البيانات"""
        data = self.get_data()
        
        if not data["serial_number"]:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال الرقم التسلسلي")
            return False
        
        if not data["model"]:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال موديل الراوتر")
            return False
        
        if not data["brand"]:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال ماركة الراوتر")
            return False
        
        if data["purchase_price"] <= 0:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال سعر شراء صحيح")
            return False
        
        return True
    
    def accept(self):
        """قبول النموذج"""
        if self.validate_data():
            super().accept()


class RoutersWidget(QWidget):
    """واجهة إدارة الراوترات"""
    
    def __init__(self, user, access_token):
        super().__init__()
        self.user = user
        self.access_token = access_token
        self.routers_data = []
        
        self.init_ui()
        self.load_routers()
    
    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(15)
        
        # شريط الأدوات
        self.create_toolbar(layout)
        
        # شريط البحث والفلاتر
        self.create_search_bar(layout)
        
        # جدول الراوترات
        self.create_routers_table(layout)
        
        # شريط الحالة
        self.create_status_bar(layout)
    
    def create_toolbar(self, layout):
        """إنشاء شريط الأدوات"""
        toolbar_frame = QFrame()
        toolbar_layout = QHBoxLayout(toolbar_frame)
        toolbar_layout.setContentsMargins(10, 10, 10, 10)
        
        # زر إضافة راوتر جديد
        add_button = QPushButton("➕ إضافة راوتر جديد")
        add_button.setObjectName("primaryButton")
        add_button.clicked.connect(self.add_router)
        toolbar_layout.addWidget(add_button)
        
        # زر تسليم راوتر
        assign_button = QPushButton("📤 تسليم راوتر")
        assign_button.setObjectName("secondaryButton")
        assign_button.clicked.connect(self.assign_router)
        toolbar_layout.addWidget(assign_button)
        
        # زر إرجاع راوتر
        return_button = QPushButton("📥 إرجاع راوتر")
        return_button.setObjectName("warningButton")
        return_button.clicked.connect(self.return_router)
        toolbar_layout.addWidget(return_button)
        
        # زر تحديث البيانات
        refresh_button = QPushButton("🔄 تحديث")
        refresh_button.clicked.connect(self.load_routers)
        toolbar_layout.addWidget(refresh_button)
        
        # مساحة فارغة
        toolbar_layout.addStretch()
        
        # زر طباعة
        print_button = QPushButton("🖨️ طباعة")
        print_button.clicked.connect(self.print_list)
        toolbar_layout.addWidget(print_button)
        
        # تطبيق الأنماط
        toolbar_frame.setStyleSheet("""
        QPushButton#primaryButton {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: bold;
        }
        
        QPushButton#primaryButton:hover {
            background: #45a049;
        }
        
        QPushButton#secondaryButton {
            background: #2196F3;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: bold;
        }
        
        QPushButton#secondaryButton:hover {
            background: #1976D2;
        }
        
        QPushButton#warningButton {
            background: #FF9800;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: bold;
        }
        
        QPushButton#warningButton:hover {
            background: #F57C00;
        }
        
        QPushButton {
            padding: 8px 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
        }
        
        QPushButton:hover {
            background: #f5f5f5;
        }
        """)
        
        layout.addWidget(toolbar_frame)
    
    def create_search_bar(self, layout):
        """إنشاء شريط البحث"""
        search_frame = QFrame()
        search_layout = QHBoxLayout(search_frame)
        search_layout.setContentsMargins(10, 10, 10, 10)
        
        # حقل البحث
        search_label = QLabel("البحث:")
        search_layout.addWidget(search_label)
        
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("ابحث بالرقم التسلسلي أو الموديل...")
        self.search_input.textChanged.connect(self.filter_routers)
        search_layout.addWidget(self.search_input)
        
        # فلتر الحالة
        status_label = QLabel("الحالة:")
        search_layout.addWidget(status_label)
        
        self.status_combo = QComboBox()
        self.status_combo.addItems(["الكل", "متاح", "مسلم", "تالف"])
        self.status_combo.currentTextChanged.connect(self.filter_routers)
        search_layout.addWidget(self.status_combo)
        
        # فلتر الماركة
        brand_label = QLabel("الماركة:")
        search_layout.addWidget(brand_label)
        
        self.brand_combo = QComboBox()
        self.brand_combo.addItem("الكل")
        self.brand_combo.currentTextChanged.connect(self.filter_routers)
        search_layout.addWidget(self.brand_combo)
        
        layout.addWidget(search_frame)
    
    def create_routers_table(self, layout):
        """إنشاء جدول الراوترات"""
        self.table = QTableWidget()
        self.table.setAlternatingRowColors(True)
        self.table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.table.setSelectionMode(QTableWidget.SelectionMode.SingleSelection)
        
        # أعمدة الجدول
        columns = [
            "الرقم", "الرقم التسلسلي", "الموديل", "الماركة", "الحالة", 
            "المشترك", "سعر الشراء", "تاريخ الشراء"
        ]
        
        self.table.setColumnCount(len(columns))
        self.table.setHorizontalHeaderLabels(columns)
        
        # تخصيص عرض الأعمدة
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)  # الرقم التسلسلي
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)  # الموديل
        
        # أحداث الجدول
        self.table.doubleClicked.connect(self.edit_router)
        
        layout.addWidget(self.table)
    
    def create_status_bar(self, layout):
        """إنشاء شريط الحالة"""
        self.status_label = QLabel("جاري تحميل البيانات...")
        self.status_label.setStyleSheet("padding: 5px; color: #666;")
        layout.addWidget(self.status_label)
    
    def load_routers(self):
        """تحميل قائمة الراوترات"""
        try:
            self.status_label.setText("جاري تحميل البيانات...")
            
            headers = {"Authorization": f"Bearer {self.access_token}"}
            url = f"http://{settings.api_host}:{settings.api_port}/api/routers/"
            response = requests.get(url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                self.routers_data = response.json()
                self.populate_table()
                self.update_brand_filter()
                self.status_label.setText(f"تم تحميل {len(self.routers_data)} راوتر")
            else:
                QMessageBox.warning(self, "خطأ", "فشل في تحميل بيانات الراوترات")
                self.status_label.setText("فشل في تحميل البيانات")
                
        except requests.exceptions.ConnectionError:
            QMessageBox.critical(self, "خطأ في الاتصال", "لا يمكن الاتصال بالخادم")
            self.status_label.setText("خطأ في الاتصال")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"خطأ غير متوقع: {str(e)}")
            self.status_label.setText("خطأ في التحميل")
    
    def update_brand_filter(self):
        """تحديث فلتر الماركات"""
        brands = set()
        for router in self.routers_data:
            brands.add(router["brand"])
        
        current_brand = self.brand_combo.currentText()
        self.brand_combo.clear()
        self.brand_combo.addItem("الكل")
        
        for brand in sorted(brands):
            self.brand_combo.addItem(brand)
        
        # استعادة الاختيار السابق
        index = self.brand_combo.findText(current_brand)
        if index >= 0:
            self.brand_combo.setCurrentIndex(index)
    
    def populate_table(self, data=None):
        """ملء الجدول بالبيانات"""
        if data is None:
            data = self.routers_data
        
        self.table.setRowCount(len(data))
        
        for row, router in enumerate(data):
            # الرقم
            self.table.setItem(row, 0, QTableWidgetItem(str(router["id"])))
            
            # الرقم التسلسلي
            self.table.setItem(row, 1, QTableWidgetItem(router["serial_number"]))
            
            # الموديل
            self.table.setItem(row, 2, QTableWidgetItem(router["model"]))
            
            # الماركة
            self.table.setItem(row, 3, QTableWidgetItem(router["brand"]))
            
            # الحالة
            status_map = {
                "available": "متاح",
                "assigned": "مسلم",
                "damaged": "تالف",
                "returned": "مرجع"
            }
            status = status_map.get(router["status"], router["status"])
            status_item = QTableWidgetItem(status)
            
            # تلوين الحالة
            if router["status"] == "available":
                status_item.setBackground(Qt.GlobalColor.green)
            elif router["status"] == "assigned":
                status_item.setBackground(Qt.GlobalColor.yellow)
            elif router["status"] == "damaged":
                status_item.setBackground(Qt.GlobalColor.red)
            
            self.table.setItem(row, 4, status_item)
            
            # المشترك
            subscriber_name = router.get("subscriber_name", "غير مسلم")
            self.table.setItem(row, 5, QTableWidgetItem(subscriber_name))
            
            # سعر الشراء
            price = f"{router['purchase_price']:.2f}"
            self.table.setItem(row, 6, QTableWidgetItem(price))
            
            # تاريخ الشراء
            purchase_date = router.get("purchase_date", "غير محدد")
            if purchase_date and purchase_date != "غير محدد":
                purchase_date = purchase_date.split("T")[0]
            self.table.setItem(row, 7, QTableWidgetItem(purchase_date))
    
    def filter_routers(self):
        """فلترة الراوترات"""
        search_text = self.search_input.text().lower()
        status_filter = self.status_combo.currentText()
        brand_filter = self.brand_combo.currentText()
        
        filtered_data = []
        
        for router in self.routers_data:
            # فلتر البحث
            if search_text and (search_text not in router["serial_number"].lower() and 
                               search_text not in router["model"].lower()):
                continue
            
            # فلتر الحالة
            status_map = {"متاح": "available", "مسلم": "assigned", "تالف": "damaged"}
            if status_filter != "الكل" and router["status"] != status_map.get(status_filter):
                continue
            
            # فلتر الماركة
            if brand_filter != "الكل" and router["brand"] != brand_filter:
                continue
            
            filtered_data.append(router)
        
        self.populate_table(filtered_data)
        self.status_label.setText(f"عرض {len(filtered_data)} من {len(self.routers_data)} راوتر")
    
    def add_router(self):
        """إضافة راوتر جديد"""
        dialog = AddRouterDialog(self.access_token, self)
        
        if dialog.exec() == QDialog.DialogCode.Accepted:
            data = dialog.get_data()
            
            try:
                headers = {"Authorization": f"Bearer {self.access_token}"}
                url = f"http://{settings.api_host}:{settings.api_port}/api/routers/"
                response = requests.post(url, json=data, headers=headers, timeout=10)
                
                if response.status_code == 200:
                    QMessageBox.information(self, "نجح", "تم إضافة الراوتر بنجاح")
                    self.load_routers()  # تحديث القائمة
                else:
                    error_msg = "فشل في إضافة الراوتر"
                    if response.status_code == 400:
                        error_data = response.json()
                        error_msg = error_data.get("detail", error_msg)
                    QMessageBox.warning(self, "خطأ", error_msg)
                    
            except Exception as e:
                QMessageBox.critical(self, "خطأ", f"خطأ في إضافة الراوتر: {str(e)}")
    
    def edit_router(self):
        """تعديل راوتر"""
        current_row = self.table.currentRow()
        if current_row >= 0:
            router_id = self.table.item(current_row, 0).text()
            QMessageBox.information(self, "تعديل راوتر", f"سيتم فتح نافذة تعديل الراوتر رقم {router_id}")
    
    def assign_router(self):
        """تسليم راوتر"""
        current_row = self.table.currentRow()
        if current_row >= 0:
            router_serial = self.table.item(current_row, 1).text()
            QMessageBox.information(self, "تسليم راوتر", f"سيتم فتح نافذة تسليم الراوتر {router_serial}")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار راوتر من القائمة")
    
    def return_router(self):
        """إرجاع راوتر"""
        current_row = self.table.currentRow()
        if current_row >= 0:
            router_serial = self.table.item(current_row, 1).text()
            QMessageBox.information(self, "إرجاع راوتر", f"سيتم فتح نافذة إرجاع الراوتر {router_serial}")
        else:
            QMessageBox.warning(self, "تحذير", "يرجى اختيار راوتر من القائمة")
    
    def print_list(self):
        """طباعة القائمة"""
        QMessageBox.information(self, "طباعة", "سيتم طباعة قائمة الراوترات")
