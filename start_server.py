#!/usr/bin/env python3
"""
تشغيل الخادم فقط - مبسط
"""
import asyncio
import uvicorn
from app.core.config import settings, create_directories
from app.db.database import init_db

async def setup():
    """إعداد التطبيق"""
    print("🚀 إعداد التطبيق...")
    create_directories()
    await init_db()
    print("✅ تم إعداد التطبيق")

def main():
    """تشغيل الخادم"""
    # إعداد التطبيق
    asyncio.run(setup())
    
    # تشغيل الخادم
    print(f"🌐 تشغيل الخادم على http://localhost:8000")
    print(f"📚 وثائق API: http://localhost:8000/docs")
    
    from app.api.main import app
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info"
    )

if __name__ == "__main__":
    main()
