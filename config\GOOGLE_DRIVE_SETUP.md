# إعداد Google Drive للمزامنة

## الخطوات المطلوبة:

### 1. إنشاء مشروع في Google Cloud Console

1. اذهب إلى [Google Cloud Console](https://console.cloud.google.com/)
2. أنشئ مشروع جديد أو اختر مشروع موجود
3. فعّل Google Drive API:
   - اذهب إلى "APIs & Services" > "Library"
   - ابحث عن "Google Drive API"
   - اضغط "Enable"

### 2. إنشاء بيانات الاعتماد

1. اذهب إلى "APIs & Services" > "Credentials"
2. اضغط "Create Credentials" > "OAuth client ID"
3. اختر "Desktop application"
4. أدخل اسم للتطبيق
5. احفظ الملف المحمل باسم `google_credentials.json` في مجلد `config/`

### 3. <PERSON>عد<PERSON> التطبيق

1. انسخ ملف `google_credentials_example.json` إلى `google_credentials.json`
2. استبدل القيم بالقيم الصحيحة من ملف بيانات الاعتماد المحمل
3. تأكد من أن الملف في المسار الصحيح: `config/google_credentials.json`

### 4. أول تشغيل

عند أول تشغيل للمزامنة:
1. سيفتح متصفح الويب تلقائياً
2. سجل الدخول بحساب Google المطلوب
3. امنح الصلاحيات المطلوبة للتطبيق
4. سيتم حفظ رمز الوصول تلقائياً للاستخدامات التالية

### 5. إعدادات المزامنة

يمكن تعديل إعدادات المزامنة في ملف `.env`:

```
# تفعيل المزامنة التلقائية
AUTO_SYNC_ENABLED=True

# فترة المزامنة بالدقائق
SYNC_INTERVAL_MINUTES=30

# تفعيل النسخ الاحتياطي التلقائي
AUTO_BACKUP_ENABLED=True

# مسار ملف بيانات الاعتماد
GOOGLE_DRIVE_CREDENTIALS_FILE=config/google_credentials.json
```

### 6. استكشاف الأخطاء

#### خطأ في المصادقة:
- تأكد من صحة ملف `google_credentials.json`
- تأكد من تفعيل Google Drive API
- احذف ملف `config/token.json` وأعد المصادقة

#### خطأ في الاتصال:
- تأكد من الاتصال بالإنترنت
- تحقق من إعدادات الجدار الناري
- تأكد من عدم حظر Google APIs

#### خطأ في الصلاحيات:
- تأكد من منح جميع الصلاحيات المطلوبة
- تحقق من إعدادات الحساب في Google Cloud Console

### 7. الأمان

- احتفظ بملف `google_credentials.json` آمناً
- لا تشارك بيانات الاعتماد مع أحد
- استخدم حساب Google مخصص للتطبيق إذا أمكن
- راجع الصلاحيات الممنوحة بانتظام

### 8. النسخ الاحتياطي اليدوي

يمكن إجراء نسخ احتياطي يدوي من خلال:
- واجهة التطبيق: قائمة "ملف" > "نسخ احتياطي"
- API: استدعاء `/api/sync/backup`
- سطر الأوامر: تشغيل `python -m sync.backup_manual`
