#!/usr/bin/env python3
"""
تشغيل التطبيق المقاوم للأخطاء - يعمل في جميع الحالات
حتى مع مشاكل الشبكة، البروكسي، VPN، أو أي مشاكل أخرى
"""
import sys
import os
import subprocess
import time
import socket
from pathlib import Path

def print_header():
    """طباعة رأس التطبيق"""
    print("=" * 60)
    print("🏢 تطبيق المحاسبة المتكامل - الإصدار المقاوم للأخطاء")
    print("=" * 60)
    print("✨ يعمل في جميع الحالات:")
    print("   • مع أو بدون إنترنت")
    print("   • مع أو بدون بروكسي/VPN")
    print("   • مع أو بدون Google Drive")
    print("   • حتى مع مشاكل الشبكة")
    print("=" * 60)

def check_python_version():
    """فحص إصدار Python"""
    if sys.version_info < (3, 8):
        print("❌ خطأ: يتطلب Python 3.8 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def setup_environment():
    """إعداد البيئة"""
    try:
        # إضافة المسار الحالي
        current_dir = str(Path.cwd())
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        # إعداد متغيرات البيئة للشبكة
        os.environ['PYTHONHTTPSVERIFY'] = '0'
        os.environ['REQUESTS_CA_BUNDLE'] = ''
        os.environ['CURL_CA_BUNDLE'] = ''
        
        # تعطيل warnings
        os.environ['PYTHONWARNINGS'] = 'ignore'
        
        print("✅ تم إعداد البيئة")
        return True
        
    except Exception as e:
        print(f"⚠️ تحذير في إعداد البيئة: {e}")
        return True

def check_requirements():
    """فحص وتثبيت المتطلبات"""
    print("📦 فحص المتطلبات...")
    
    required_packages = [
        'fastapi',
        'uvicorn',
        'sqlalchemy',
        'pydantic',
        'PyQt6'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'PyQt6':
                import PyQt6
            else:
                __import__(package)
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package} - مفقود")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n📥 تثبيت المكتبات المفقودة: {', '.join(missing_packages)}")
        try:
            for package in missing_packages:
                print(f"   📦 تثبيت {package}...")
                result = subprocess.run([
                    sys.executable, "-m", "pip", "install", package
                ], capture_output=True, text=True, timeout=120)
                
                if result.returncode == 0:
                    print(f"   ✅ تم تثبيت {package}")
                else:
                    print(f"   ⚠️ فشل تثبيت {package}: {result.stderr}")
        except Exception as e:
            print(f"⚠️ خطأ في التثبيت: {e}")
            print("💡 يمكنك تثبيت المتطلبات يدوياً:")
            print("   pip install fastapi uvicorn sqlalchemy pydantic PyQt6")
    
    return True

def check_network():
    """فحص الاتصال بالشبكة"""
    print("🌐 فحص الاتصال بالشبكة...")
    
    test_hosts = [
        ("*******", 53, "Google DNS"),
        ("*******", 53, "Cloudflare DNS"),
        ("**************", 53, "OpenDNS")
    ]
    
    connected = False
    for host, port, name in test_hosts:
        try:
            socket.create_connection((host, port), timeout=5)
            print(f"   ✅ متصل عبر {name}")
            connected = True
            break
        except:
            print(f"   ❌ لا يمكن الاتصال عبر {name}")
    
    if not connected:
        print("   📴 لا يوجد اتصال بالإنترنت - سيتم العمل في الوضع غير المتصل")
    
    return connected

def find_available_port(start_port=8000):
    """البحث عن منفذ متاح"""
    for port in range(start_port, start_port + 20):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('', port))
                return port
        except OSError:
            continue
    return start_port

def start_server():
    """تشغيل الخادم"""
    print("\n🚀 تشغيل الخادم...")
    
    port = find_available_port(8000)
    if port != 8000:
        print(f"⚠️ المنفذ 8000 مشغول، سيتم استخدام المنفذ {port}")
    
    try:
        # تشغيل الخادم المقاوم للأخطاء
        process = subprocess.Popen([
            sys.executable, "robust_server.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        print(f"✅ تم تشغيل الخادم (PID: {process.pid})")
        print(f"🌐 الخادم متاح على:")
        print(f"   • http://localhost:{port}")
        print(f"   • http://127.0.0.1:{port}")
        print(f"📚 وثائق API: http://localhost:{port}/docs")
        
        return process
        
    except Exception as e:
        print(f"❌ فشل في تشغيل الخادم: {e}")
        return None

def start_gui():
    """تشغيل الواجهة"""
    print("\n🖥️ تشغيل الواجهة...")
    
    try:
        process = subprocess.Popen([
            sys.executable, "robust_gui.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        print(f"✅ تم تشغيل الواجهة (PID: {process.pid})")
        return process
        
    except Exception as e:
        print(f"❌ فشل في تشغيل الواجهة: {e}")
        return None

def show_menu():
    """عرض القائمة"""
    print("\n📋 اختر وضع التشغيل:")
    print("1. تشغيل كامل (خادم + واجهة)")
    print("2. خادم فقط")
    print("3. واجهة فقط")
    print("4. فحص النظام")
    print("5. خروج")
    
    while True:
        try:
            choice = input("\nاختيارك (1-5): ").strip()
            if choice in ['1', '2', '3', '4', '5']:
                return int(choice)
            else:
                print("❌ اختيار غير صحيح، يرجى اختيار رقم من 1 إلى 5")
        except KeyboardInterrupt:
            print("\n👋 تم الإلغاء")
            return 5

def system_check():
    """فحص شامل للنظام"""
    print("\n🔍 فحص شامل للنظام...")
    print("-" * 40)
    
    # فحص Python
    python_ok = check_python_version()
    
    # فحص البيئة
    env_ok = setup_environment()
    
    # فحص المتطلبات
    req_ok = check_requirements()
    
    # فحص الشبكة
    net_ok = check_network()
    
    # فحص الملفات
    print("\n📁 فحص الملفات الأساسية...")
    essential_files = [
        "app/api/main.py",
        "app/db/database.py",
        "app/core/config.py",
        "robust_server.py"
    ]
    
    files_ok = True
    for file_path in essential_files:
        if Path(file_path).exists():
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} - مفقود")
            files_ok = False
    
    # النتيجة النهائية
    print("\n📊 نتيجة الفحص:")
    print(f"   • Python: {'✅' if python_ok else '❌'}")
    print(f"   • البيئة: {'✅' if env_ok else '❌'}")
    print(f"   • المتطلبات: {'✅' if req_ok else '❌'}")
    print(f"   • الشبكة: {'✅' if net_ok else '📴'}")
    print(f"   • الملفات: {'✅' if files_ok else '❌'}")
    
    if python_ok and env_ok and files_ok:
        print("\n🎉 النظام جاهز للتشغيل!")
        return True
    else:
        print("\n⚠️ هناك مشاكل في النظام، لكن التطبيق قد يعمل في الوضع المحدود")
        return False

def main():
    """الدالة الرئيسية"""
    print_header()
    
    # فحص أساسي
    if not check_python_version():
        input("اضغط Enter للخروج...")
        return 1
    
    setup_environment()
    
    while True:
        choice = show_menu()
        
        if choice == 1:  # تشغيل كامل
            print("\n🚀 تشغيل التطبيق الكامل...")
            server_process = start_server()
            time.sleep(3)  # انتظار بدء الخادم
            gui_process = start_gui()
            
            if server_process or gui_process:
                print("\n✅ تم تشغيل التطبيق!")
                print("💡 لإيقاف التطبيق، اضغط Ctrl+C")
                try:
                    while True:
                        time.sleep(1)
                except KeyboardInterrupt:
                    print("\n⏹️ إيقاف التطبيق...")
                    if server_process:
                        server_process.terminate()
                    if gui_process:
                        gui_process.terminate()
                    break
            
        elif choice == 2:  # خادم فقط
            server_process = start_server()
            if server_process:
                print("\n💡 لإيقاف الخادم، اضغط Ctrl+C")
                try:
                    server_process.wait()
                except KeyboardInterrupt:
                    print("\n⏹️ إيقاف الخادم...")
                    server_process.terminate()
            
        elif choice == 3:  # واجهة فقط
            gui_process = start_gui()
            if gui_process:
                try:
                    gui_process.wait()
                except KeyboardInterrupt:
                    print("\n⏹️ إيقاف الواجهة...")
                    gui_process.terminate()
            
        elif choice == 4:  # فحص النظام
            system_check()
            input("\nاضغط Enter للمتابعة...")
            
        elif choice == 5:  # خروج
            print("\n👋 شكراً لاستخدام التطبيق!")
            break
    
    return 0

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print("\n👋 وداعاً!")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")
        sys.exit(1)
